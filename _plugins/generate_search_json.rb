require 'yaml'
require 'json'
require 'nokogiri'

module <PERSON><PERSON><PERSON>
  class GenerateSearchJson < Generator
    safe true
    priority :low

    def generate(site)
      # Define the input files
      posts_meta_path = File.join(site.source, '_data', 'posts_meta.yml')
      projects_meta_path = File.join(site.source, '_data', 'projects_meta.yml')
      tags_path = File.join(site.source, '_data', 'tags.yml')

      # Read the YAML files
      posts_meta = YAML.load_file(posts_meta_path) if File.exist?(posts_meta_path)
      projects_meta = YAML.load_file(projects_meta_path) if File.exist?(projects_meta_path)
      tags_data = YAML.load_file(tags_path) if File.exist?(tags_path)

      # Load existing IDs
      existing_ids = load_existing_ids(site)

      # Combine the data
      combined_data = []
      new_keys_added = false

      [posts_meta, projects_meta].compact.each do |meta|
        meta['list'].each do |key, value|
          if value['path'].to_s.strip.empty?
            Jekyll.logger.error "Skipping key due to missing or invalid path: #{key}"
            next
          end

          if existing_ids.key?(key)
            Jekyll.logger.info "Duplicate key skipped: #{key}"
            combined_data << existing_ids[key]
            next
          end

          # Mark that a new key is being added
          new_keys_added = true

          # Load and clean content from the path using Nokogiri
          content_path = File.join(site.source, value['path'])
          content = if File.exist?(content_path)
                      raw_content = File.read(content_path)

                      # Remove YAML front matter if present
                      raw_content = raw_content.sub(/\A---.*?---/m, '') if raw_content.include?('---')

                      # Parse with Nokogiri and extract meaningful text
                      doc = Nokogiri::HTML(raw_content)
                      clean_text = doc.xpath('//body//*[not(self::script) and not(self::style)]').map(&:text).join(' ')
                      clean_text.gsub(/\s+/, ' ').strip # Replace multiple spaces and \n with a single space
                    else
                      ""
                    end

          # Load tags from tags.yml
          tags = tags_data[value['path']] || []

          combined_data << {
            id: key,
            title: value['post_name'],
            path: value['path'],
            date: value['post_date'],
            tags: tags,
            content: content
          }
        end
      end

      # Write the file only if new keys are added
      if new_keys_added
        search_json_path = File.join(site.source, 'assets', 'search.json')
        File.open(search_json_path, 'w') do |file|
          file.write(JSON.pretty_generate(combined_data))
        end

        # Register the file as a static file
        site.static_files << Jekyll::StaticFile.new(
          site, site.source, 'assets', 'search.json'
        )

        Jekyll.logger.info "Generated search.json with #{combined_data.size} unique items."
      else
        Jekyll.logger.info "No new keys found. Skipping search.json regeneration."
      end
    end

    private

    # Load existing IDs from the current search.json
    def load_existing_ids(site)
      search_json_path = File.join(site.source, 'assets', 'search.json')
      return {} unless File.exist?(search_json_path)

      existing_data = JSON.parse(File.read(search_json_path)) rescue []
      existing_data.each_with_object({}) do |item, ids|
        ids[item['id']] = item
      end
    end
  end
end
