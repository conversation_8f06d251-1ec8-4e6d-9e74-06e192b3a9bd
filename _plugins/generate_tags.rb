module Jekyll
    class TagPageGenerator < Generator
      safe true
  
      def generate(site)
        # Collect unique tags from your data file (or site.posts)
        all_tags = []
  
        site.data['tags'].each do |_path, tags|
          all_tags.concat(tags)
        end
  
        unique_tags = all_tags.uniq
  
        # Directory for tag pages
        tag_dir = File.join(site.source, 'tags')
        Dir.mkdir(tag_dir) unless Dir.exist?(tag_dir)
  
        # Generate a tag page for each unique tag
        unique_tags.each do |tag|
          slug = tag.downcase.gsub(' ', '-')
          file_path = File.join(tag_dir, "#{slug}.md")
  
          # Check if the file already exists
          if File.exist?(file_path)
            next
          end
  
          # Create the file if it doesn't exist
          File.open(file_path, 'w') do |file|
            file.puts "---"
            file.puts "layout: tag"
            file.puts "tag: #{tag}"
            file.puts "permalink: /tags/#{slug}/"
            file.puts "---"
          end
  
          puts "TagPageGenerator: Generated tag page for '#{tag}' at #{file_path}"
        end
      end
    end
  end
  