[{"id": "003_<PERSON><PERSON><PERSON>_Solver_Towards_Future_of_Simulation_Deep_Dive", "title": "Neural Solver Towards Future of Simulation: Deep Dive", "path": "pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content.html", "date": "08-09-2024", "tags": ["<PERSON><PERSON><PERSON>", "Simulation", "Neural Operator"], "content": "Neural Solver Towards Future of Simulation: Deep Dive Date: 08-09-2024 | Author: <PERSON><PERSON><PERSON><PERSON> Song Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro Table of Contents Neural Operator Operator Learning Intro: DeepONet Motivation Neural Operator Design Main Layer Design Theoretical Consideration Application of Neural Solvers In previous post, we explored Neural ODEs and PINNs approaches for neural solvers. In this post, we examine Neural Operator, a method that opens up new possibilities for efficient and accurate simulations. Additionally, practical applications of these previously introduced neural solvers will be presented to showcase their real-world potential. Neural Operator Neural Operators (NOs) leverage the power of DL to model the underlying functions and mappings directly. This approach is called operator learning. 💡 Operator Learning: Learning operators that are mappings between infinite-dimensional function spaces. Operator Learning Intro: DeepONet The term “operator learning” may not be clear at first glance. We will illustrate this concept with the example of DeepONet, one of the milestone achievements in operator learning. Original paper: DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Good examples and illustrations from DeepONet: Learning nonlinear operators (lululxvi.github.io) will be introduced here. Function vs. Operator Function: \\R^{d_1} → \\R^{d_2} : Below is a function for \\R^{28 \\times 28} \\rightarrow \\R^{10} . DeepONet: Learning nonlinear operators (lululxvi.github.io) Operator: \\text{function} (∞-\\dim) \\mapsto \\text{function} (∞-\\dim) Derivative (local): x(t) \\mapsto x'(t) . Integral (global): x(t) \\mapsto \\int K(s, t)x(s)ds . Dynamic system: Differential equation Problem Setup The goal is to learn an operator G that maps a function u to another function G(u) . Formally: G : u \\mapsto G(u), \\\\ G(u) : y ∈ \\R^d \\mapsto G(u)(y) ∈ \\R More concretely, given a differential equation of form: \\mathcal L s = u We want operator G:u \\mapsto s . Inputs: u at sensors \\{x_1, x_2, \\cdots, x_m\\} and y ∈ \\R^d . What exactly input x and y look like? It depends on the given DE. Output: G(u)(y) ∈ \\R . DeepONet: Learning nonlinear operators (lululxvi.github.io) Architecture DeepONet consists of two encoder networks: Branch Net : Encodes the input function into a latent representation. i.e. this encoder is used for encoding the discretized input function. Trunk Net : Encodes the coordinates or specific points where the output is evaluated. i.e. the second encoder is used for encoding the location of the output functions. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) The goal is to approximate the target operator G with a NN G_{\\theta} : G(u)(y) ≈ G_{\\theta}(u)(y) = \\sum^p_{k=1} b_k(u) · t_k(y) Prior knowledge: u and y are independent. G(u)(y) is a function of y conditioning on u . Branch net: b_k(u) , u -dependent coefficients. Trunk net: t_k(y) , basis functions of y . Example Case: Diffusion-reaction system Given a PDE: DeepONet: Learning nonlinear operators (lululxvi.github.io) Input for branch net becomes u(x_i) and input for trunk net becomes (x_i,t_i) . DeepONet: Learning nonlinear operators (lululxvi.github.io) For a fixed function u:\\R \\rightarrow \\R , P points are sampled for each (x_i,t_i) with input function estimation u(x_i) and solution function s(x_t,t_i) obtained via simulation: \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) Number of total dataset: \\text{number of function }u \\times P . The NN output approximates as: G_\\theta(u)(x_i,t_i) \\approx s(x_i,t_i) . A naive data-driven supervised loss can be used. A physics-informed loss can also be used. Discussion DeepONet introduces a theorem ensuring it is a universal approximator for operator learning. This theorem underpins DeepONet's ability to accurately learn and generalize mappings between infinite-dimensional spaces. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Pros Accurately approximates mappings between infinite-dimensional spaces. Efficient for real-time simulations once trained. Cons Requires a large amount of training data. Not resolution invariant; only takes input functions at a fixed discretization. Motivation Now, let's dive into an important milestone for neural solvers in operator learning: Neural Operators. The DeepONet from last section is also considered as a neural operator in nowadays. However, we'll discuss it in the context of the original Neural Operator paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) . In this context, the following figure highlights that DeepONet lacks discretization invariance: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) What is discretization invariance and why it is important? Resolution of Simulated Data Neural Operator: Learning Maps Between Function Spaces (arxiv.org) The figure displays simulation data with increasing mesh density from left to right: sparse (low resolution), medium (medium resolution), and dense (high resolution). Higher resolution provides better quality simulations but requires more computational resources. Training models on low-resolution data and inferring on high-resolution data is desirable but can lead to performance degradation due to out-of-distribution issues. Discretization invariance allows models to handle different resolutions effectively, ensuring models trained on low-resolution data generalize well to high-resolution settings, avoiding the out-of-distribution issues that degrade performance. Framework Consider the generic family of PDEs: (\\mathcal{L}_au)(x) = f(x), \\quad x \\in D\\\\ u(x) = 0,\\quad x ∈ ∂D. where D ⊂ \\R^d is spatial domain for the PDE with points x ∈ D in the the spatial domain. e.g. Standard second order elliptic PDE - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D Input function a:D\\rightarrow \\R^{d_a} is defined with bounded domain D and a ∈ \\mathcal A . It includes coefficients, boundaries, and/or initial conditions. More concretely, initial state u_0 can be an input function. Output function u : D' → \\R^{d_u} is defined with bounded domain D'\\sub \\R^{d'} and u \\in \\mathcal U : target solution functions . More concretely, solution u_t at some desired timestep t can be target solution function. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) We can simply assume D=D' . Optimal operator for this PDE can be defined as \\mathcal{G} = \\mathcal{L}_a^{-1}f:\\mathcal A \\rightarrow \\mathcal U : a \\mapsto u . i.e. we aim to find the solution at the target timestep given initial conditions. Theoretical Consideration Given a linear operator \\mathcal{L}_a , there exists a unique function called Green’s function G_a : D \\times D \\to \\mathbb{R} s.t. \\mathcal{L}_a G_a(x, \\cdot) = \\delta_x where \\delta_x is the delta measure on \\R^d centered at x . Note that a is denoted on G_a since it is dependent on \\mathcal L_a . Then true operator \\mathcal{G} can be written as: \\mathcal{G}: a \\mapsto u(x) = \\int_D G_a(x,y)f(y) \\: dy Since \\mathcal{L}_a is linear operator with respect to variable x , computation order with \\int_D \\cdot \\, dy can be exchanged: f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) With fixed f , we can readily check that solution u is only dependent on input function a . How to find such Green’s function? If the operator \\mathcal{L}_a admits a complete set of eigenfunctions (function version of eigenvector) \\Psi_i(x) , i.e. \\mathcal{L}_a\\Psi_i=\\lambda_i \\Psi_i : Completeness means \\delta(x-y)=\\sum_i \\Psi_i(x)^*\\Psi_i(y) holds, where * denotes complex conjugation. Then, the Green’s function is given by: G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} Analogous to the kernel trick in ML, G_a(x,y) can be viewed as a kernel with feature function: \\phi(y)=[\\Psi_1(y) / \\sqrt{\\lambda_1},\\Psi_2(y) / \\sqrt{\\lambda_2}, \\cdots] This perspective of Green's function will inform the design of the neural operator layer, serving as an inductive bias, which will be introduced later. Neural Operator Design Main goal of neural operator is to construct NN \\mathcal G_{\\theta} to approximates \\mathcal G . With discretization invariance and operator learning framework. Without needing knowledge of the underlying PDE. Loss objective Given the dataset \\{a_i, u_i\\}^N_{i=1} (observations), simple supervised loss is used: \\min_θ\\dfrac{1}{N}\\sum^N_{i=1} \\Vert u_i − \\mathcal G_θ(a_i) \\Vert^2_\\mathcal U Architecture The original Neural Operator (NO) architecture has three main components: \\mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Lifting mapping P : P : \\R^{d_a} → \\R^{d_{v_0}} . To take advantage of NN, it sends input a_i to a high dimensional representation space. Main Layer W_i : It is designed for “Iterative Kernel Integration” scheme. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} is local linear operators. Projection mapping Q : Q : \\R^{d_{v_T}} → \\R^{d_u} . Projects the high-dimensional representation back, obtaining the final solution u . The lifting and projection mappings are straightforward. The main layer's design, based on the inductive bias of Green’s function, will be discussed in the next section. Main Layer Design Integral Kernel Operators As discussed earlier, since Green's function can be viewed as a kernel, we can modify the solution u(x) = \\int_D G_a(x,y)f(y) \\: dy by introducing a kernel network \\kappa_\\phi : u(x) = \\int_D \\kappa_\\phi(x,y,a(x),a(y))f(y) \\: dy Based on this, to train the NO without knowing f , we design an iterative update scheme: v_{t+1}(x) = \\sigma\\Big( W v_t(x) + \\int_{B(x,r)} \\kappa_{\\phi}\\big(x,y,a(x),a(y)\\big) v_t(y)\\: dy \\Big) \\tag{1} where t=1,\\ldots,N-1 holds. Here, v_t(x) and v_{t+1}(x) are input & output representation respectively. This iterative scheme functions as a layer of the neural operator. 💡 Until this stage, the iterative update scheme was designed with Green’s function as an inductive bias for neural operator layers. The detailed design of \\int \\kappa_{\\phi} v_t in equation (1) leads to various NO layer versions, resulting in different NO variants. This section will introduce the FNO, as proposed in the original paper. FNO (Fourier Neural Operator) Let’s follow the expression of the original paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Convolution Theorem Recall the definition of convolution: f*g=\\int f(x)g(y-x)dx . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . Convolution transforms to multiplication under Fourier Transform (FT): \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) Using FT can bypass the often intractable integral computation of convolution. Thus, FNO designs the kernel integral operator using FT for the neural operator layer. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) R_{\\phi} : Learnable transform directly parameterized in Fourier space. Complex-valued (k_{\\max} ×d_v ×d_v) -tensor is differentiable in torch. FFT is also differentiable in torch: torch.fft — PyTorch 2.3 documentation . Theoretical Consideration Note For proof, lifting and projection operators Q , P are set to the identity. The authors found learning these operators beneficial in practice. Further proof to cover this is required. \\text{NO}_n : Neural operator network with n layers. Approximation Theorem Neural Operator: Learning Maps Between Function Spaces (arxiv.org) This theorem states that a NO network with sufficient depth N can approximate any target operator G^\\dagger within a specified tolerance. See Appendix G for detail. Discretization Invariance Neural Operator: Learning Maps Between Function Spaces (arxiv.org) This theorem states that NO network achieves discretization-invariance. See Appendix E for detail. But let’s clarify one important thing here: How to define discretization-invariant? Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Even after obtaining trained \\mathcal G , evaluating \\mathcal G(a) is some what theoretical: Since \\mathcal G is operator, input should be a function a . To pass a function as input, we need to evaluate a on discretization D_L with L points. With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . We denote this discretized evaluation of a for \\mathcal G as \\hat{\\mathcal G}(D_L, a|_{D_L}) . If the distance between \\hat{\\mathcal G}(D_L, a|_{D_L}) and \\mathcal G(a) in a function space, R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) , is small for sufficiently large discretization size, then we can say \\mathcal G is discretization-invariant. Note that model prediction with trained NO becomes \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) naturally: e.g. mapping \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \\mapsto u_t(D_L) . Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Application of Neural Solvers We will conclude this series of posts, “Neural Solver Towards the Future of Simulation,” by briefly introducing application papers on neural solvers. Figures in this section are from the respective papers. FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators Developed by NVIDIA, FourCastNet utilizes the Adaptive Fourier Neural Operator (AFNO) to predict weather forecasts with high resolution. ERA5 serves as the ground truth for comparison. ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs | ICLR 2024 Oral ClimODE represents 2nd-order PDEs as a system of 1st-order ODEs to apply the Neural ODE framework. NN f_\\theta is given as: Project Page: ClimODE (yogeshverma1998.github.io) BENO: Boundary-embedded Neural Operators for Elliptic PDEs | ICLR 2024 This neural operator architecture addresses the challenges posed by complex boundary geometries. The dual-branch design builds two different types of edges on the same graph separately. Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint. Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian). Neural Solver Towards Future of Simulation: Deep Dive Date: 08-09-2024 | Author: Ki-Ung Song Neural Solver Towards Future of Simulation: Deep Dive Date: 08-09-2024 | Author: Ki-Ung Song Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro Table of Contents Neural Operator Operator Learning Intro: DeepONet Motivation Neural Operator Design Main Layer Design Theoretical Consideration Application of Neural Solvers In previous post, we explored Neural ODEs and PINNs approaches for neural solvers. In this post, we examine Neural Operator, a method that opens up new possibilities for efficient and accurate simulations. Additionally, practical applications of these previously introduced neural solvers will be presented to showcase their real-world potential. Neural Operator Neural Operators (NOs) leverage the power of DL to model the underlying functions and mappings directly. This approach is called operator learning. 💡 Operator Learning: Learning operators that are mappings between infinite-dimensional function spaces. Operator Learning Intro: DeepONet The term “operator learning” may not be clear at first glance. We will illustrate this concept with the example of DeepONet, one of the milestone achievements in operator learning. Original paper: DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Good examples and illustrations from DeepONet: Learning nonlinear operators (lululxvi.github.io) will be introduced here. Function vs. Operator Function: \\R^{d_1} → \\R^{d_2} : Below is a function for \\R^{28 \\times 28} \\rightarrow \\R^{10} . DeepONet: Learning nonlinear operators (lululxvi.github.io) Operator: \\text{function} (∞-\\dim) \\mapsto \\text{function} (∞-\\dim) Derivative (local): x(t) \\mapsto x'(t) . Integral (global): x(t) \\mapsto \\int K(s, t)x(s)ds . Dynamic system: Differential equation Problem Setup The goal is to learn an operator G that maps a function u to another function G(u) . Formally: G : u \\mapsto G(u), \\\\ G(u) : y ∈ \\R^d \\mapsto G(u)(y) ∈ \\R More concretely, given a differential equation of form: \\mathcal L s = u We want operator G:u \\mapsto s . Inputs: u at sensors \\{x_1, x_2, \\cdots, x_m\\} and y ∈ \\R^d . What exactly input x and y look like? It depends on the given DE. Output: G(u)(y) ∈ \\R . DeepONet: Learning nonlinear operators (lululxvi.github.io) Architecture DeepONet consists of two encoder networks: Branch Net : Encodes the input function into a latent representation. i.e. this encoder is used for encoding the discretized input function. Trunk Net : Encodes the coordinates or specific points where the output is evaluated. i.e. the second encoder is used for encoding the location of the output functions. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) The goal is to approximate the target operator G with a NN G_{\\theta} : G(u)(y) ≈ G_{\\theta}(u)(y) = \\sum^p_{k=1} b_k(u) · t_k(y) Prior knowledge: u and y are independent. G(u)(y) is a function of y conditioning on u . Branch net: b_k(u) , u -dependent coefficients. Trunk net: t_k(y) , basis functions of y . Example Case: Diffusion-reaction system Given a PDE: DeepONet: Learning nonlinear operators (lululxvi.github.io) Input for branch net becomes u(x_i) and input for trunk net becomes (x_i,t_i) . DeepONet: Learning nonlinear operators (lululxvi.github.io) For a fixed function u:\\R \\rightarrow \\R , P points are sampled for each (x_i,t_i) with input function estimation u(x_i) and solution function s(x_t,t_i) obtained via simulation: \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) Number of total dataset: \\text{number of function }u \\times P . The NN output approximates as: G_\\theta(u)(x_i,t_i) \\approx s(x_i,t_i) . A naive data-driven supervised loss can be used. A physics-informed loss can also be used. Discussion DeepONet introduces a theorem ensuring it is a universal approximator for operator learning. This theorem underpins DeepONet's ability to accurately learn and generalize mappings between infinite-dimensional spaces. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Pros Accurately approximates mappings between infinite-dimensional spaces. Efficient for real-time simulations once trained. Cons Requires a large amount of training data. Not resolution invariant; only takes input functions at a fixed discretization. Motivation Now, let's dive into an important milestone for neural solvers in operator learning: Neural Operators. The DeepONet from last section is also considered as a neural operator in nowadays. However, we'll discuss it in the context of the original Neural Operator paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) . In this context, the following figure highlights that DeepONet lacks discretization invariance: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) What is discretization invariance and why it is important? Resolution of Simulated Data Neural Operator: Learning Maps Between Function Spaces (arxiv.org) The figure displays simulation data with increasing mesh density from left to right: sparse (low resolution), medium (medium resolution), and dense (high resolution). Higher resolution provides better quality simulations but requires more computational resources. Training models on low-resolution data and inferring on high-resolution data is desirable but can lead to performance degradation due to out-of-distribution issues. Discretization invariance allows models to handle different resolutions effectively, ensuring models trained on low-resolution data generalize well to high-resolution settings, avoiding the out-of-distribution issues that degrade performance. Framework Consider the generic family of PDEs: (\\mathcal{L}_au)(x) = f(x), \\quad x \\in D\\\\ u(x) = 0,\\quad x ∈ ∂D. where D ⊂ \\R^d is spatial domain for the PDE with points x ∈ D in the the spatial domain. e.g. Standard second order elliptic PDE - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D Input function a:D\\rightarrow \\R^{d_a} is defined with bounded domain D and a ∈ \\mathcal A . It includes coefficients, boundaries, and/or initial conditions. More concretely, initial state u_0 can be an input function. Output function u : D' → \\R^{d_u} is defined with bounded domain D'\\sub \\R^{d'} and u \\in \\mathcal U : target solution functions . More concretely, solution u_t at some desired timestep t can be target solution function. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) We can simply assume D=D' . Optimal operator for this PDE can be defined as \\mathcal{G} = \\mathcal{L}_a^{-1}f:\\mathcal A \\rightarrow \\mathcal U : a \\mapsto u . i.e. we aim to find the solution at the target timestep given initial conditions. Theoretical Consideration Given a linear operator \\mathcal{L}_a , there exists a unique function called Green’s function G_a : D \\times D \\to \\mathbb{R} s.t. \\mathcal{L}_a G_a(x, \\cdot) = \\delta_x where \\delta_x is the delta measure on \\R^d centered at x . Note that a is denoted on G_a since it is dependent on \\mathcal L_a . Then true operator \\mathcal{G} can be written as: \\mathcal{G}: a \\mapsto u(x) = \\int_D G_a(x,y)f(y) \\: dy Since \\mathcal{L}_a is linear operator with respect to variable x , computation order with \\int_D \\cdot \\, dy can be exchanged: f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) With fixed f , we can readily check that solution u is only dependent on input function a . How to find such Green’s function? If the operator \\mathcal{L}_a admits a complete set of eigenfunctions (function version of eigenvector) \\Psi_i(x) , i.e. \\mathcal{L}_a\\Psi_i=\\lambda_i \\Psi_i : Completeness means \\delta(x-y)=\\sum_i \\Psi_i(x)^*\\Psi_i(y) holds, where * denotes complex conjugation. Then, the Green’s function is given by: G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} Analogous to the kernel trick in ML, G_a(x,y) can be viewed as a kernel with feature function: \\phi(y)=[\\Psi_1(y) / \\sqrt{\\lambda_1},\\Psi_2(y) / \\sqrt{\\lambda_2}, \\cdots] This perspective of Green's function will inform the design of the neural operator layer, serving as an inductive bias, which will be introduced later. Neural Operator Design Main goal of neural operator is to construct NN \\mathcal G_{\\theta} to approximates \\mathcal G . With discretization invariance and operator learning framework. Without needing knowledge of the underlying PDE. Loss objective Given the dataset \\{a_i, u_i\\}^N_{i=1} (observations), simple supervised loss is used: \\min_θ\\dfrac{1}{N}\\sum^N_{i=1} \\Vert u_i − \\mathcal G_θ(a_i) \\Vert^2_\\mathcal U Architecture The original Neural Operator (NO) architecture has three main components: \\mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Lifting mapping P : P : \\R^{d_a} → \\R^{d_{v_0}} . To take advantage of NN, it sends input a_i to a high dimensional representation space. Main Layer W_i : It is designed for “Iterative Kernel Integration” scheme. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} is local linear operators. Projection mapping Q : Q : \\R^{d_{v_T}} → \\R^{d_u} . Projects the high-dimensional representation back, obtaining the final solution u . The lifting and projection mappings are straightforward. The main layer's design, based on the inductive bias of Green’s function, will be discussed in the next section. Main Layer Design Integral Kernel Operators As discussed earlier, since Green's function can be viewed as a kernel, we can modify the solution u(x) = \\int_D G_a(x,y)f(y) \\: dy by introducing a kernel network \\kappa_\\phi : u(x) = \\int_D \\kappa_\\phi(x,y,a(x),a(y))f(y) \\: dy Based on this, to train the NO without knowing f , we design an iterative update scheme: v_{t+1}(x) = \\sigma\\Big( W v_t(x) + \\int_{B(x,r)} \\kappa_{\\phi}\\big(x,y,a(x),a(y)\\big) v_t(y)\\: dy \\Big) \\tag{1} where t=1,\\ldots,N-1 holds. Here, v_t(x) and v_{t+1}(x) are input & output representation respectively. This iterative scheme functions as a layer of the neural operator. 💡 Until this stage, the iterative update scheme was designed with Green’s function as an inductive bias for neural operator layers. The detailed design of \\int \\kappa_{\\phi} v_t in equation (1) leads to various NO layer versions, resulting in different NO variants. This section will introduce the FNO, as proposed in the original paper. FNO (Fourier Neural Operator) Let’s follow the expression of the original paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Convolution Theorem Recall the definition of convolution: f*g=\\int f(x)g(y-x)dx . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . Convolution transforms to multiplication under Fourier Transform (FT): \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) Using FT can bypass the often intractable integral computation of convolution. Thus, FNO designs the kernel integral operator using FT for the neural operator layer. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) R_{\\phi} : Learnable transform directly parameterized in Fourier space. Complex-valued (k_{\\max} ×d_v ×d_v) -tensor is differentiable in torch. FFT is also differentiable in torch: torch.fft — PyTorch 2.3 documentation . Theoretical Consideration Note For proof, lifting and projection operators Q , P are set to the identity. The authors found learning these operators beneficial in practice. Further proof to cover this is required. \\text{NO}_n : Neural operator network with n layers. Approximation Theorem Neural Operator: Learning Maps Between Function Spaces (arxiv.org) This theorem states that a NO network with sufficient depth N can approximate any target operator G^\\dagger within a specified tolerance. See Appendix G for detail. Discretization Invariance Neural Operator: Learning Maps Between Function Spaces (arxiv.org) This theorem states that NO network achieves discretization-invariance. See Appendix E for detail. But let’s clarify one important thing here: How to define discretization-invariant? Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Even after obtaining trained \\mathcal G , evaluating \\mathcal G(a) is some what theoretical: Since \\mathcal G is operator, input should be a function a . To pass a function as input, we need to evaluate a on discretization D_L with L points. With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . We denote this discretized evaluation of a for \\mathcal G as \\hat{\\mathcal G}(D_L, a|_{D_L}) . If the distance between \\hat{\\mathcal G}(D_L, a|_{D_L}) and \\mathcal G(a) in a function space, R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) , is small for sufficiently large discretization size, then we can say \\mathcal G is discretization-invariant. Note that model prediction with trained NO becomes \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) naturally: e.g. mapping \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \\mapsto u_t(D_L) . Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Application of Neural Solvers We will conclude this series of posts, “Neural Solver Towards the Future of Simulation,” by briefly introducing application papers on neural solvers. Figures in this section are from the respective papers. FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators Developed by NVIDIA, FourCastNet utilizes the Adaptive Fourier Neural Operator (AFNO) to predict weather forecasts with high resolution. ERA5 serves as the ground truth for comparison. ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs | ICLR 2024 Oral ClimODE represents 2nd-order PDEs as a system of 1st-order ODEs to apply the Neural ODE framework. NN f_\\theta is given as: Project Page: ClimODE (yogeshverma1998.github.io) BENO: Boundary-embedded Neural Operators for Elliptic PDEs | ICLR 2024 This neural operator architecture addresses the challenges posed by complex boundary geometries. The dual-branch design builds two different types of edges on the same graph separately. Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint. Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian). Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Deep Dive - Current Post Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation: Intro Table of Contents Neural Operator Operator Learning Intro: DeepONet Motivation Neural Operator Design Main Layer Design Theoretical Consideration Application of Neural Solvers Table of Contents Neural Operator Operator Learning Intro: DeepONet Motivation Neural Operator Design Main Layer Design Theoretical Consideration Application of Neural Solvers Table of Contents Table of Contents Neural Operator Operator Learning Intro: DeepONet Motivation Neural Operator Design Main Layer Design Theoretical Consideration Application of Neural Solvers Neural Operator Neural Operator Operator Learning Intro: DeepONet Operator Learning Intro: DeepONet Motivation Motivation Neural Operator Design Neural Operator Design Main Layer Design Main Layer Design Theoretical Consideration Theoretical Consideration Application of Neural Solvers Application of Neural Solvers In previous post, we explored Neural ODEs and PINNs approaches for neural solvers. In this post, we examine Neural Operator, a method that opens up new possibilities for efficient and accurate simulations. Additionally, practical applications of these previously introduced neural solvers will be presented to showcase their real-world potential. Neural Operator Neural Operators (NOs) leverage the power of DL to model the underlying functions and mappings directly. This approach is called operator learning. 💡 Operator Learning: Learning operators that are mappings between infinite-dimensional function spaces. 💡 💡 Operator Learning: Learning operators that are mappings between infinite-dimensional function spaces. Operator Learning: Learning operators that are mappings between infinite-dimensional function spaces. Operator Learning Intro: DeepONet The term “operator learning” may not be clear at first glance. We will illustrate this concept with the example of DeepONet, one of the milestone achievements in operator learning. Original paper: DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Original paper: DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Good examples and illustrations from DeepONet: Learning nonlinear operators (lululxvi.github.io) will be introduced here. Good examples and illustrations from DeepONet: Learning nonlinear operators (lululxvi.github.io) will be introduced here. DeepONet: Learning nonlinear operators (lululxvi.github.io) Function vs. Operator Function vs. Operator Function: \\R^{d_1} → \\R^{d_2} : Below is a function for \\R^{28 \\times 28} \\rightarrow \\R^{10} . DeepONet: Learning nonlinear operators (lululxvi.github.io) Function: \\R^{d_1} → \\R^{d_2} : Below is a function for \\R^{28 \\times 28} \\rightarrow \\R^{10} . DeepONet: Learning nonlinear operators (lululxvi.github.io) \\R^{d_1} → \\R^{d_2} \\R^{d_1} → \\R^{d_2} \\R^{28 \\times 28} \\rightarrow \\R^{10} \\R^{28 \\times 28} \\rightarrow \\R^{10} DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) Operator: \\text{function} (∞-\\dim) \\mapsto \\text{function} (∞-\\dim) Derivative (local): x(t) \\mapsto x'(t) . Integral (global): x(t) \\mapsto \\int K(s, t)x(s)ds . Dynamic system: Differential equation Operator: \\text{function} (∞-\\dim) \\mapsto \\text{function} (∞-\\dim) Derivative (local): x(t) \\mapsto x'(t) . Integral (global): x(t) \\mapsto \\int K(s, t)x(s)ds . Dynamic system: Differential equation \\text{function} (∞-\\dim) \\mapsto \\text{function} (∞-\\dim) \\text{function} (∞-\\dim) \\mapsto \\text{function} (∞-\\dim) Derivative (local): x(t) \\mapsto x'(t) . Derivative (local): x(t) \\mapsto x'(t) . x(t) \\mapsto x'(t) x(t) \\mapsto x'(t) Integral (global): x(t) \\mapsto \\int K(s, t)x(s)ds . Integral (global): x(t) \\mapsto \\int K(s, t)x(s)ds . x(t) \\mapsto \\int K(s, t)x(s)ds x(t) \\mapsto \\int K(s, t)x(s)ds Dynamic system: Differential equation Dynamic system: Differential equation Problem Setup Problem Setup The goal is to learn an operator G that maps a function u to another function G(u) . Formally: G G u u G(u) G(u) G : u \\mapsto G(u), \\\\ G(u) : y ∈ \\R^d \\mapsto G(u)(y) ∈ \\R G : u \\mapsto G(u), \\\\ G(u) : y ∈ \\R^d \\mapsto G(u)(y) ∈ \\R G : u \\mapsto G(u), \\\\ G(u) : y ∈ \\R^d \\mapsto G(u)(y) ∈ \\R G : u \\mapsto G(u), \\\\ G(u) : y ∈ \\R^d \\mapsto G(u)(y) ∈ \\R More concretely, given a differential equation of form: \\mathcal L s = u \\mathcal L s = u \\mathcal L s = u \\mathcal L s = u We want operator G:u \\mapsto s . G:u \\mapsto s G:u \\mapsto s Inputs: u at sensors \\{x_1, x_2, \\cdots, x_m\\} and y ∈ \\R^d . What exactly input x and y look like? It depends on the given DE. Inputs: u at sensors \\{x_1, x_2, \\cdots, x_m\\} and y ∈ \\R^d . What exactly input x and y look like? It depends on the given DE. u u \\{x_1, x_2, \\cdots, x_m\\} \\{x_1, x_2, \\cdots, x_m\\} y ∈ \\R^d y ∈ \\R^d What exactly input x and y look like? It depends on the given DE. What exactly input x and y look like? It depends on the given DE. What exactly input x x and y y look like? It depends on the given DE. Output: G(u)(y) ∈ \\R . DeepONet: Learning nonlinear operators (lululxvi.github.io) Output: G(u)(y) ∈ \\R . DeepONet: Learning nonlinear operators (lululxvi.github.io) G(u)(y) ∈ \\R G(u)(y) ∈ \\R DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) Architecture Architecture DeepONet consists of two encoder networks: two encoder Branch Net : Encodes the input function into a latent representation. i.e. this encoder is used for encoding the discretized input function. Branch Net : Encodes the input function into a latent representation. i.e. this encoder is used for encoding the discretized input function. Branch Net Trunk Net : Encodes the coordinates or specific points where the output is evaluated. i.e. the second encoder is used for encoding the location of the output functions. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Trunk Net : Encodes the coordinates or specific points where the output is evaluated. i.e. the second encoder is used for encoding the location of the output functions. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Trunk Net DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) The goal is to approximate the target operator G with a NN G_{\\theta} : G G G_{\\theta} G_{\\theta} G(u)(y) ≈ G_{\\theta}(u)(y) = \\sum^p_{k=1} b_k(u) · t_k(y) G(u)(y) ≈ G_{\\theta}(u)(y) = \\sum^p_{k=1} b_k(u) · t_k(y) G(u)(y) ≈ G_{\\theta}(u)(y) = \\sum^p_{k=1} b_k(u) · t_k(y) G(u)(y) ≈ G_{\\theta}(u)(y) = \\sum^p_{k=1} b_k(u) · t_k(y) Prior knowledge: u and y are independent. Prior knowledge: u and y are independent. u u y y G(u)(y) is a function of y conditioning on u . Branch net: b_k(u) , u -dependent coefficients. Trunk net: t_k(y) , basis functions of y . G(u)(y) is a function of y conditioning on u . Branch net: b_k(u) , u -dependent coefficients. Trunk net: t_k(y) , basis functions of y . G(u)(y) G(u)(y) y y u u Branch net: b_k(u) , u -dependent coefficients. Branch net: b_k(u) , u -dependent coefficients. b_k(u) b_k(u) u u Trunk net: t_k(y) , basis functions of y . Trunk net: t_k(y) , basis functions of y . t_k(y) t_k(y) y y Example Case: Diffusion-reaction system Example Case: Diffusion-reaction system Given a PDE: DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) Input for branch net becomes u(x_i) and input for trunk net becomes (x_i,t_i) . u(x_i) u(x_i) (x_i,t_i) (x_i,t_i) DeepONet: Learning nonlinear operators (lululxvi.github.io) For a fixed function u:\\R \\rightarrow \\R , P points are sampled for each (x_i,t_i) with input function estimation u(x_i) and solution function s(x_t,t_i) obtained via simulation: \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) Number of total dataset: \\text{number of function }u \\times P . DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) DeepONet: Learning nonlinear operators (lululxvi.github.io) For a fixed function u:\\R \\rightarrow \\R , P points are sampled for each (x_i,t_i) with input function estimation u(x_i) and solution function s(x_t,t_i) obtained via simulation: \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) For a fixed function u:\\R \\rightarrow \\R , P points are sampled for each (x_i,t_i) with input function estimation u(x_i) and solution function s(x_t,t_i) obtained via simulation: \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) u:\\R \\rightarrow \\R u:\\R \\rightarrow \\R P P (x_i,t_i) (x_i,t_i) u(x_i) u(x_i) s(x_t,t_i) s(x_t,t_i) \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) \\left((x_i,t_i), u(x_i), s(x_i,t_i)\\right) Number of total dataset: \\text{number of function }u \\times P . Number of total dataset: \\text{number of function }u \\times P . \\text{number of function }u \\times P \\text{number of function }u \\times P The NN output approximates as: G_\\theta(u)(x_i,t_i) \\approx s(x_i,t_i) . G_\\theta(u)(x_i,t_i) \\approx s(x_i,t_i) G_\\theta(u)(x_i,t_i) \\approx s(x_i,t_i) A naive data-driven supervised loss can be used. A naive data-driven supervised loss can be used. A physics-informed loss can also be used. A physics-informed loss can also be used. Discussion Discussion DeepONet introduces a theorem ensuring it is a universal approximator for operator learning. This theorem underpins DeepONet's ability to accurately learn and generalize mappings between infinite-dimensional spaces. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet introduces a theorem ensuring it is a universal approximator for operator learning. This theorem underpins DeepONet's ability to accurately learn and generalize mappings between infinite-dimensional spaces. DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org) Pros Accurately approximates mappings between infinite-dimensional spaces. Efficient for real-time simulations once trained. Pros Accurately approximates mappings between infinite-dimensional spaces. Efficient for real-time simulations once trained. Pros Accurately approximates mappings between infinite-dimensional spaces. Accurately approximates mappings between infinite-dimensional spaces. Efficient for real-time simulations once trained. Efficient for real-time simulations once trained. Cons Requires a large amount of training data. Not resolution invariant; only takes input functions at a fixed discretization. Cons Requires a large amount of training data. Not resolution invariant; only takes input functions at a fixed discretization. Cons Requires a large amount of training data. Requires a large amount of training data. Not resolution invariant; only takes input functions at a fixed discretization. Not resolution invariant; only takes input functions at a fixed discretization. Motivation Now, let's dive into an important milestone for neural solvers in operator learning: Neural Operators. The DeepONet from last section is also considered as a neural operator in nowadays. However, we'll discuss it in the context of the original Neural Operator paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) . In this context, the following figure highlights that DeepONet lacks discretization invariance: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) What is discretization invariance and why it is important? What is discretization invariance and why it is important? Resolution of Simulated Data Resolution of Simulated Data Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) The figure displays simulation data with increasing mesh density from left to right: sparse (low resolution), medium (medium resolution), and dense (high resolution). The figure displays simulation data with increasing mesh density from left to right: sparse (low resolution), medium (medium resolution), and dense (high resolution). Higher resolution provides better quality simulations but requires more computational resources. Training models on low-resolution data and inferring on high-resolution data is desirable but can lead to performance degradation due to out-of-distribution issues. Higher resolution provides better quality simulations but requires more computational resources. Training models on low-resolution data and inferring on high-resolution data is desirable but can lead to performance degradation due to out-of-distribution issues. Discretization invariance allows models to handle different resolutions effectively, ensuring models trained on low-resolution data generalize well to high-resolution settings, avoiding the out-of-distribution issues that degrade performance. Discretization invariance allows models to handle different resolutions effectively, ensuring models trained on low-resolution data generalize well to high-resolution settings, avoiding the out-of-distribution issues that degrade performance. Framework Framework Consider the generic family of PDEs: (\\mathcal{L}_au)(x) = f(x), \\quad x \\in D\\\\ u(x) = 0,\\quad x ∈ ∂D. (\\mathcal{L}_au)(x) = f(x), \\quad x \\in D\\\\ u(x) = 0,\\quad x ∈ ∂D. (\\mathcal{L}_au)(x) = f(x), \\quad x \\in D\\\\ u(x) = 0,\\quad x ∈ ∂D. (\\mathcal{L}_au)(x) = f(x), \\quad x \\in D\\\\ u(x) = 0,\\quad x ∈ ∂D. where D ⊂ \\R^d is spatial domain for the PDE with points x ∈ D in the the spatial domain. D ⊂ \\R^d D ⊂ \\R^d x ∈ D x ∈ D e.g. Standard second order elliptic PDE - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D e.g. Standard second order elliptic PDE - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D - \\nabla \\cdot (a(x) \\nabla u(x)) = f(x), \\quad x \\in D \\\\ u(x) = 0, \\quad x \\in \\partial D Input function a:D\\rightarrow \\R^{d_a} is defined with bounded domain D and a ∈ \\mathcal A . a:D\\rightarrow \\R^{d_a} a:D\\rightarrow \\R^{d_a} D D a ∈ \\mathcal A a ∈ \\mathcal A It includes coefficients, boundaries, and/or initial conditions. It includes coefficients, boundaries, and/or initial conditions. More concretely, initial state u_0 can be an input function. More concretely, initial state u_0 can be an input function. u_0 u_0 Output function u : D' → \\R^{d_u} is defined with bounded domain D'\\sub \\R^{d'} and u \\in \\mathcal U : target solution functions . u : D' → \\R^{d_u} u : D' → \\R^{d_u} D'\\sub \\R^{d'} D'\\sub \\R^{d'} u \\in \\mathcal U u \\in \\mathcal U target solution functions More concretely, solution u_t at some desired timestep t can be target solution function. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) More concretely, solution u_t at some desired timestep t can be target solution function. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) More concretely, solution u_t u_t at some desired timestep t t can be target solution function. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) We can simply assume D=D' . We can simply assume D=D' . D=D' D=D' Optimal operator for this PDE can be defined as \\mathcal{G} = \\mathcal{L}_a^{-1}f:\\mathcal A \\rightarrow \\mathcal U : a \\mapsto u . i.e. we aim to find the solution at the target timestep given initial conditions. \\mathcal{G} = \\mathcal{L}_a^{-1}f:\\mathcal A \\rightarrow \\mathcal U : a \\mapsto u \\mathcal{G} = \\mathcal{L}_a^{-1}f:\\mathcal A \\rightarrow \\mathcal U : a \\mapsto u Theoretical Consideration Theoretical Consideration Given a linear operator \\mathcal{L}_a , there exists a unique function called Green’s function G_a : D \\times D \\to \\mathbb{R} s.t. \\mathcal{L}_a \\mathcal{L}_a G_a : D \\times D \\to \\mathbb{R} G_a : D \\times D \\to \\mathbb{R} \\mathcal{L}_a G_a(x, \\cdot) = \\delta_x \\mathcal{L}_a G_a(x, \\cdot) = \\delta_x \\mathcal{L}_a G_a(x, \\cdot) = \\delta_x \\mathcal{L}_a G_a(x, \\cdot) = \\delta_x where \\delta_x is the delta measure on \\R^d centered at x . \\delta_x \\delta_x \\R^d \\R^d x x Note that a is denoted on G_a since it is dependent on \\mathcal L_a . Note that a is denoted on G_a since it is dependent on \\mathcal L_a . a a G_a G_a \\mathcal L_a \\mathcal L_a Then true operator \\mathcal{G} can be written as: \\mathcal{G} \\mathcal{G} \\mathcal{G}: a \\mapsto u(x) = \\int_D G_a(x,y)f(y) \\: dy \\mathcal{G}: a \\mapsto u(x) = \\int_D G_a(x,y)f(y) \\: dy \\mathcal{G}: a \\mapsto u(x) = \\int_D G_a(x,y)f(y) \\: dy \\mathcal{G}: a \\mapsto u(x) = \\int_D G_a(x,y)f(y) \\: dy Since \\mathcal{L}_a is linear operator with respect to variable x , computation order with \\int_D \\cdot \\, dy can be exchanged: f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) Since \\mathcal{L}_a is linear operator with respect to variable x , computation order with \\int_D \\cdot \\, dy can be exchanged: f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) \\mathcal{L}_a \\mathcal{L}_a x x \\int_D \\cdot \\, dy \\int_D \\cdot \\, dy f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) f(x)= \\int_D \\delta_x(y) f(y) dy = \\int_D \\mathcal{L}_a G_a(x,y)f(y)dy \\\\ = \\mathcal{L}_a \\left(\\int_D G_a(x,y)f(y)dy\\right) With fixed f , we can readily check that solution u is only dependent on input function a . f f u u a a How to find such Green’s function? How to find such Green’s function? If the operator \\mathcal{L}_a admits a complete set of eigenfunctions (function version of eigenvector) \\Psi_i(x) , i.e. \\mathcal{L}_a\\Psi_i=\\lambda_i \\Psi_i : \\mathcal{L}_a \\mathcal{L}_a \\Psi_i(x) \\Psi_i(x) \\mathcal{L}_a\\Psi_i=\\lambda_i \\Psi_i \\mathcal{L}_a\\Psi_i=\\lambda_i \\Psi_i Completeness means \\delta(x-y)=\\sum_i \\Psi_i(x)^*\\Psi_i(y) holds, where * denotes complex conjugation. Completeness means \\delta(x-y)=\\sum_i \\Psi_i(x)^*\\Psi_i(y) holds, where * denotes complex conjugation. \\delta(x-y)=\\sum_i \\Psi_i(x)^*\\Psi_i(y) \\delta(x-y)=\\sum_i \\Psi_i(x)^*\\Psi_i(y) * * Then, the Green’s function is given by: G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} Then, the Green’s function is given by: G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} G_a(x,y)=\\sum_i \\dfrac{\\Psi_i(x)^*\\Psi_i(y)}{\\lambda_i} Analogous to the kernel trick in ML, G_a(x,y) can be viewed as a kernel with feature function: G_a(x,y) G_a(x,y) can be viewed as a kernel \\phi(y)=[\\Psi_1(y) / \\sqrt{\\lambda_1},\\Psi_2(y) / \\sqrt{\\lambda_2}, \\cdots] \\phi(y)=[\\Psi_1(y) / \\sqrt{\\lambda_1},\\Psi_2(y) / \\sqrt{\\lambda_2}, \\cdots] \\phi(y)=[\\Psi_1(y) / \\sqrt{\\lambda_1},\\Psi_2(y) / \\sqrt{\\lambda_2}, \\cdots] \\phi(y)=[\\Psi_1(y) / \\sqrt{\\lambda_1},\\Psi_2(y) / \\sqrt{\\lambda_2}, \\cdots] This perspective of Green's function will inform the design of the neural operator layer, serving as an inductive bias, which will be introduced later. This perspective of Green's function will inform the design of the neural operator layer, serving as an inductive bias, which will be introduced later. Neural Operator Design Main goal of neural operator is to construct NN \\mathcal G_{\\theta} to approximates \\mathcal G . Main goal of neural operator is to construct NN \\mathcal G_{\\theta} \\mathcal G_{\\theta} to approximates \\mathcal G \\mathcal G . With discretization invariance and operator learning framework. With discretization invariance and operator learning framework. Without needing knowledge of the underlying PDE. Without needing knowledge of the underlying PDE. Loss objective Loss objective Given the dataset \\{a_i, u_i\\}^N_{i=1} (observations), simple supervised loss is used: \\{a_i, u_i\\}^N_{i=1} \\{a_i, u_i\\}^N_{i=1} \\min_θ\\dfrac{1}{N}\\sum^N_{i=1} \\Vert u_i − \\mathcal G_θ(a_i) \\Vert^2_\\mathcal U \\min_θ\\dfrac{1}{N}\\sum^N_{i=1} \\Vert u_i − \\mathcal G_θ(a_i) \\Vert^2_\\mathcal U \\min_θ\\dfrac{1}{N}\\sum^N_{i=1} \\Vert u_i − \\mathcal G_θ(a_i) \\Vert^2_\\mathcal U \\min_θ\\dfrac{1}{N}\\sum^N_{i=1} \\Vert u_i − \\mathcal G_θ(a_i) \\Vert^2_\\mathcal U Architecture Architecture The original Neural Operator (NO) architecture has three main components: \\mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a) \\mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a) \\mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a) \\mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Lifting mapping P : P : \\R^{d_a} → \\R^{d_{v_0}} . To take advantage of NN, it sends input a_i to a high dimensional representation space. Lifting mapping P : P : \\R^{d_a} → \\R^{d_{v_0}} . To take advantage of NN, it sends input a_i to a high dimensional representation space. P P P : \\R^{d_a} → \\R^{d_{v_0}} . P : \\R^{d_a} → \\R^{d_{v_0}} . P : \\R^{d_a} → \\R^{d_{v_0}} P : \\R^{d_a} → \\R^{d_{v_0}} To take advantage of NN, it sends input a_i to a high dimensional representation space. To take advantage of NN, it sends input a_i to a high dimensional representation space. a_i a_i Main Layer W_i : It is designed for “Iterative Kernel Integration” scheme. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} is local linear operators. Main Layer W_i : It is designed for “Iterative Kernel Integration” scheme. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} is local linear operators. W_i W_i It is designed for “Iterative Kernel Integration” scheme. It is designed for “Iterative Kernel Integration” scheme. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} is local linear operators. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} is local linear operators. W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} W_i: \\R^{d_{v_i}} → \\R^{d_{v_{i+1}}} Projection mapping Q : Q : \\R^{d_{v_T}} → \\R^{d_u} . Projects the high-dimensional representation back, obtaining the final solution u . Projection mapping Q : Q : \\R^{d_{v_T}} → \\R^{d_u} . Projects the high-dimensional representation back, obtaining the final solution u . Q Q Q : \\R^{d_{v_T}} → \\R^{d_u} . Q : \\R^{d_{v_T}} → \\R^{d_u} . Q : \\R^{d_{v_T}} → \\R^{d_u} Q : \\R^{d_{v_T}} → \\R^{d_u} Projects the high-dimensional representation back, obtaining the final solution u . Projects the high-dimensional representation back, obtaining the final solution u . u u The lifting and projection mappings are straightforward. The main layer's design, based on the inductive bias of Green’s function, will be discussed in the next section. Main Layer Design Integral Kernel Operators Integral Kernel Operators As discussed earlier, since Green's function can be viewed as a kernel, we can modify the solution u(x) = \\int_D G_a(x,y)f(y) \\: dy by introducing a kernel network \\kappa_\\phi : u(x) = \\int_D G_a(x,y)f(y) \\: dy u(x) = \\int_D G_a(x,y)f(y) \\: dy \\kappa_\\phi \\kappa_\\phi u(x) = \\int_D \\kappa_\\phi(x,y,a(x),a(y))f(y) \\: dy u(x) = \\int_D \\kappa_\\phi(x,y,a(x),a(y))f(y) \\: dy u(x) = \\int_D \\kappa_\\phi(x,y,a(x),a(y))f(y) \\: dy u(x) = \\int_D \\kappa_\\phi(x,y,a(x),a(y))f(y) \\: dy Based on this, to train the NO without knowing f , we design an iterative update scheme: f f v_{t+1}(x) = \\sigma\\Big( W v_t(x) + \\int_{B(x,r)} \\kappa_{\\phi}\\big(x,y,a(x),a(y)\\big) v_t(y)\\: dy \\Big) \\tag{1} v_{t+1}(x) = \\sigma\\Big( W v_t(x) + \\int_{B(x,r)} \\kappa_{\\phi}\\big(x,y,a(x),a(y)\\big) v_t(y)\\: dy \\Big) \\tag{1} v_{t+1}(x) = \\sigma\\Big( W v_t(x) + \\int_{B(x,r)} \\kappa_{\\phi}\\big(x,y,a(x),a(y)\\big) v_t(y)\\: dy \\Big) \\tag{1} v_{t+1}(x) = \\sigma\\Big( W v_t(x) + \\int_{B(x,r)} \\kappa_{\\phi}\\big(x,y,a(x),a(y)\\big) v_t(y)\\: dy \\Big) \\tag{1} where t=1,\\ldots,N-1 holds. t=1,\\ldots,N-1 t=1,\\ldots,N-1 Here, v_t(x) and v_{t+1}(x) are input & output representation respectively. Here, v_t(x) and v_{t+1}(x) are input & output representation respectively. v_t(x) v_t(x) v_{t+1}(x) v_{t+1}(x) This iterative scheme functions as a layer of the neural operator. This iterative scheme functions as a layer of the neural operator. 💡 Until this stage, the iterative update scheme was designed with Green’s function as an inductive bias for neural operator layers. 💡 💡 Until this stage, the iterative update scheme was designed with Green’s function as an inductive bias for neural operator layers. Until this stage, the iterative update scheme was designed with Green’s function as an inductive bias for neural operator layers. The detailed design of \\int \\kappa_{\\phi} v_t in equation (1) leads to various NO layer versions, resulting in different NO variants. This section will introduce the FNO, as proposed in the original paper. \\int \\kappa_{\\phi} v_t \\int \\kappa_{\\phi} v_t FNO (Fourier Neural Operator) FNO (Fourier Neural Operator) Let’s follow the expression of the original paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Let’s follow the expression of the original paper: Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Convolution Theorem Recall the definition of convolution: f*g=\\int f(x)g(y-x)dx . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . Convolution transforms to multiplication under Fourier Transform (FT): \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) Using FT can bypass the often intractable integral computation of convolution. Convolution Theorem Recall the definition of convolution: f*g=\\int f(x)g(y-x)dx . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . Convolution transforms to multiplication under Fourier Transform (FT): \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) Using FT can bypass the often intractable integral computation of convolution. Recall the definition of convolution: f*g=\\int f(x)g(y-x)dx . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . Recall the definition of convolution: f*g=\\int f(x)g(y-x)dx . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . f*g=\\int f(x)g(y-x)dx f*g=\\int f(x)g(y-x)dx The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . The kernel integral operator can be seen as a convolution operator: \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t . \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t \\mathcal K(\\phi)v_t \\approx K(\\phi) * v_t Convolution transforms to multiplication under Fourier Transform (FT): \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) Convolution transforms to multiplication under Fourier Transform (FT): \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) \\mathcal{F}(f *g) = \\mathcal{F}(f)\\mathcal{F}(g) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) f *g = \\mathcal{F}^{-1}(\\mathcal{F}(f)\\mathcal{F}(g)) Using FT can bypass the often intractable integral computation of convolution. Using FT can bypass the often intractable integral computation of convolution. Using FT can bypass the often intractable integral computation of convolution. Thus, FNO designs the kernel integral operator using FT for the neural operator layer. Thus, FNO designs the kernel integral operator using FT for the neural operator layer. Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) R_{\\phi} : Learnable transform directly parameterized in Fourier space. Complex-valued (k_{\\max} ×d_v ×d_v) -tensor is differentiable in torch. R_{\\phi} : Learnable transform directly parameterized in Fourier space. Complex-valued (k_{\\max} ×d_v ×d_v) -tensor is differentiable in torch. R_{\\phi} R_{\\phi} Complex-valued (k_{\\max} ×d_v ×d_v) -tensor is differentiable in torch. Complex-valued (k_{\\max} ×d_v ×d_v) -tensor is differentiable in torch. (k_{\\max} ×d_v ×d_v) (k_{\\max} ×d_v ×d_v) FFT is also differentiable in torch: torch.fft — PyTorch 2.3 documentation . FFT is also differentiable in torch: torch.fft — PyTorch 2.3 documentation . torch.fft — PyTorch 2.3 documentation Theoretical Consideration Note Note For proof, lifting and projection operators Q , P are set to the identity. The authors found learning these operators beneficial in practice. Further proof to cover this is required. For proof, lifting and projection operators Q , P are set to the identity. The authors found learning these operators beneficial in practice. Further proof to cover this is required. Q Q P P \\text{NO}_n : Neural operator network with n layers. \\text{NO}_n : Neural operator network with n layers. \\text{NO}_n \\text{NO}_n n n Approximation Theorem Approximation Theorem Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) This theorem states that a NO network with sufficient depth N can approximate any target operator G^\\dagger within a specified tolerance. This theorem states that a NO network with sufficient depth N can approximate any target operator G^\\dagger within a specified tolerance. N N G^\\dagger G^\\dagger See Appendix G for detail. See Appendix G for detail. Discretization Invariance Discretization Invariance Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) This theorem states that NO network achieves discretization-invariance. This theorem states that NO network achieves discretization-invariance. See Appendix E for detail. But let’s clarify one important thing here: How to define discretization-invariant? Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Even after obtaining trained \\mathcal G , evaluating \\mathcal G(a) is some what theoretical: Since \\mathcal G is operator, input should be a function a . To pass a function as input, we need to evaluate a on discretization D_L with L points. With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . We denote this discretized evaluation of a for \\mathcal G as \\hat{\\mathcal G}(D_L, a|_{D_L}) . If the distance between \\hat{\\mathcal G}(D_L, a|_{D_L}) and \\mathcal G(a) in a function space, R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) , is small for sufficiently large discretization size, then we can say \\mathcal G is discretization-invariant. See Appendix E for detail. But let’s clarify one important thing here: How to define discretization-invariant? Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Even after obtaining trained \\mathcal G , evaluating \\mathcal G(a) is some what theoretical: Since \\mathcal G is operator, input should be a function a . To pass a function as input, we need to evaluate a on discretization D_L with L points. With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . We denote this discretized evaluation of a for \\mathcal G as \\hat{\\mathcal G}(D_L, a|_{D_L}) . If the distance between \\hat{\\mathcal G}(D_L, a|_{D_L}) and \\mathcal G(a) in a function space, R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) , is small for sufficiently large discretization size, then we can say \\mathcal G is discretization-invariant. How to define discretization-invariant? Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Even after obtaining trained \\mathcal G , evaluating \\mathcal G(a) is some what theoretical: Since \\mathcal G is operator, input should be a function a . Even after obtaining trained \\mathcal G , evaluating \\mathcal G(a) is some what theoretical: Since \\mathcal G is operator, input should be a function a . \\mathcal G \\mathcal G \\mathcal G(a) \\mathcal G(a) \\mathcal G \\mathcal G a a To pass a function as input, we need to evaluate a on discretization D_L with L points. With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . To pass a function as input, we need to evaluate a on discretization D_L with L points. With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . a a D_L D_L L L With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . With points \\{ x_1,\\cdots,x_L \\} , we pass function a as evaluated points \\{ a(x_1),\\cdots, a(x_L) \\} to \\mathcal G . \\{ x_1,\\cdots,x_L \\} \\{ x_1,\\cdots,x_L \\} a a \\{ a(x_1),\\cdots, a(x_L) \\} \\{ a(x_1),\\cdots, a(x_L) \\} \\mathcal G \\mathcal G We denote this discretized evaluation of a for \\mathcal G as \\hat{\\mathcal G}(D_L, a|_{D_L}) . We denote this discretized evaluation of a for \\mathcal G as \\hat{\\mathcal G}(D_L, a|_{D_L}) . a a \\mathcal G \\mathcal G \\hat{\\mathcal G}(D_L, a|_{D_L}) \\hat{\\mathcal G}(D_L, a|_{D_L}) If the distance between \\hat{\\mathcal G}(D_L, a|_{D_L}) and \\mathcal G(a) in a function space, R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) , is small for sufficiently large discretization size, then we can say \\mathcal G is discretization-invariant. If the distance between \\hat{\\mathcal G}(D_L, a|_{D_L}) and \\mathcal G(a) in a function space, R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) , is small for sufficiently large discretization size, then we can say \\mathcal G is discretization-invariant. \\hat{\\mathcal G}(D_L, a|_{D_L}) \\hat{\\mathcal G}(D_L, a|_{D_L}) \\mathcal G(a) \\mathcal G(a) R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) R_K(\\mathcal G, \\hat{\\mathcal G}, D_L) \\mathcal G \\mathcal G Note that model prediction with trained NO becomes \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) naturally: e.g. mapping \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \\mapsto u_t(D_L) . Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Note that model prediction with trained NO becomes \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) naturally: e.g. mapping \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \\mapsto u_t(D_L) . Neural Operator: Learning Maps Between Function Spaces (arxiv.org) \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \\mapsto u_t(D_L) \\hat{\\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \\mapsto u_t(D_L) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Neural Operator: Learning Maps Between Function Spaces (arxiv.org) Application of Neural Solvers We will conclude this series of posts, “Neural Solver Towards the Future of Simulation,” by briefly introducing application papers on neural solvers. Figures in this section are from the respective papers. FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators Developed by NVIDIA, FourCastNet utilizes the Adaptive Fourier Neural Operator (AFNO) to predict weather forecasts with high resolution. ERA5 serves as the ground truth for comparison. FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators Developed by NVIDIA, FourCastNet utilizes the Adaptive Fourier Neural Operator (AFNO) to predict weather forecasts with high resolution. ERA5 serves as the ground truth for comparison. FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators Developed by NVIDIA, FourCastNet utilizes the Adaptive Fourier Neural Operator (AFNO) to predict weather forecasts with high resolution. ERA5 serves as the ground truth for comparison. ERA5 serves as the ground truth for comparison. ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs | ICLR 2024 Oral ClimODE represents 2nd-order PDEs as a system of 1st-order ODEs to apply the Neural ODE framework. NN f_\\theta is given as: Project Page: ClimODE (yogeshverma1998.github.io) ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs | ICLR 2024 Oral ClimODE represents 2nd-order PDEs as a system of 1st-order ODEs to apply the Neural ODE framework. NN f_\\theta is given as: Project Page: ClimODE (yogeshverma1998.github.io) ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs | ICLR 2024 Oral ClimODE represents 2nd-order PDEs as a system of 1st-order ODEs to apply the Neural ODE framework. NN f_\\theta is given as: NN f_\\theta is given as: f_\\theta f_\\theta Project Page: ClimODE (yogeshverma1998.github.io) Project Page: ClimODE (yogeshverma1998.github.io) ClimODE (yogeshverma1998.github.io) BENO: Boundary-embedded Neural Operators for Elliptic PDEs | ICLR 2024 This neural operator architecture addresses the challenges posed by complex boundary geometries. The dual-branch design builds two different types of edges on the same graph separately. Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint. Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian). BENO: Boundary-embedded Neural Operators for Elliptic PDEs | ICLR 2024 This neural operator architecture addresses the challenges posed by complex boundary geometries. The dual-branch design builds two different types of edges on the same graph separately. Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint. Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian). BENO: Boundary-embedded Neural Operators for Elliptic PDEs BENO: Boundary-embedded Neural Operators for Elliptic PDEs | ICLR 2024 This neural operator architecture addresses the challenges posed by complex boundary geometries. The dual-branch design builds two different types of edges on the same graph separately. Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint. Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint. Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian). Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian)."}, {"id": "002_<PERSON><PERSON>al_Solver_Towards_Future_of_Simulation_Exploration", "title": "Neural Solver Towards Future of Simulation: Exploration", "path": "pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content.html", "date": "08-09-2024", "tags": ["<PERSON><PERSON><PERSON>", "Simulation", "Neural ODE", "PINN"], "content": "Neural Solver Towards Future of Simulation: Exploration Date: 08-09-2024 | Author: <PERSON><PERSON><PERSON><PERSON> Song Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Intro Table of Contents Neural ODE Motivation Forward Computation Backward Computation Application PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Overview Related Topics Building on the foundational insights from our last post on neural solvers, we explore two main neural solver approaches in this post: Neural Ordinary Differential Equation (Neural ODE) Physics-Informed Neural Network (PINN) Neural ODE Motivation The literature presents numerous variants of Neural ODE. However, this post will be anchored in the foundational principles outlined in the original paper: Neural Ordinary Differential Equations (arxiv.org) Let’s start rethinking the residual connection: z_{t+1} = z_t + f(z_t, θ_t) where t ∈ \\{0, \\dots , T\\} , z_t ∈ \\R^D is hidden state of t -th layers, and \\theta_t is t -th layers weight. Recall the previously introduced Euler method: y_{n+1} = y_n + hf(t_n, y_n) By comparing this with the residual connection, we see a clear similarity: the residual connection can be interpreted as a single step of the Euler method with step-size h=1 . This allows transitioning from discrete layers to a continuous-time formulation, parameterizing the continuous dynamics between hidden states as evolving continuously over time: \\begin{align}\\dfrac{dz(t)}{dt} = f_θ(z(t), t) \\tag{1}\\end{align} Assume t\\in[0,1] . Note that this can also be interpreted as a NN with infinite depth. Forward Computation Now we have a sophisticated formulation for NN. But how can we compute the output of this DL model explicitly? While this continuous-time approach appears elegant, it's not immediately obvious how to calculate the model's output. Mathematical Solution: Given initial value z(t_0)=z(0) , the model output z(t_N)=z(1) is obtained via simply integrating the both side of (1): z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} But you should know that integration is not a “simple” process in the real world. Integration is only mathematically meaningful, but in reality, it often turns out to be a shiny but impractical concept. Actually, solving the differential equation is equivalent to approximating the integral in many cases. We can express the approximation of the RHS in (2) with ODESolve() operation. It leads to the following expression: z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) Assume that this kind of approximation is computed by black-box called “ODE solver”. ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used. Only two observations are possible: Input z_0 at the beginning t_0 and output z_1 at the end of the trajectory t_N since ODE solver is a black-box. Backward Computation But due to its black-box nature, typical gradient computation becomes infeasible . It means that {\\partial z(t_N)}/\\partial \\theta is not obtainable with the given z(t_N) , so we cannot apply chain rule to get {\\partial L}/{\\partial \\theta} for weight update. Then how can we actually train this kind of DL model? How do we apply back-propagation? The main idea to tackle this is to approximate the gradient {\\partial L}/{\\partial \\theta} for back-propagation. Let’s find out how this becomes possible. Before that we can typical assume supervised loss with the model’s output as: \\mathcal L(\\theta) \\approx L(z(t_N), y_{true}) \\eqqcolon L(z(t_N)) This is expressed as L(z(t_N)) = L \\big( \\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) \\big) Recall that our goal is to approximate the gradients of L with respect to its parameters \\theta . We achieve this through step-by-step computation of various quantities, as illustrated in the figure below. You may not understand this now, but it will become clear soon. Neural Ordinary Differential Equations (Neural ODEs): Rethinking Architecture | by Gwen Xiao | Medium Step 1: Focus on what we can compute and generalize it One quantity is straightforward to compute: \\dfrac{\\partial L}{\\partial z(t_N)} \\tag{*} Let’s generalize the above quantity to a component called adjoint a(t) : a(t) = \\dfrac{\\partial L}{\\partial z(t)} The above quantity (*) is now expressed as a component a(t_N) . Then the evolving dynamics of a(\\cdot) can be derived as follows \\dfrac{d a(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} \\tag{3} For derivation details, please refer to the original paper. This post will focus on providing a conceptual overview for better understanding. By applying integration to LHS of (3) and properly substituting with (3), the following can be obtained: a(t_0) = a(t_N) + \\int_{t_N}^{t_0} \\dfrac{da(t)}{dt} dt = a(t_N) - \\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} dt \\tag{4} What’s the meaning of this? The derived dynamics (3) is a form of ODE and from the last post, we know that the ODE can be solved reversed starting from a(t_N) . And it is equivalent to computing the integration in RHS of (4). From the discussion of previous section we can expect that the integration in RHS of (4) can be approximated via ODE solver. Step 2: Generalize again to other quantities We can define the following quantities assuming a_{θ}(t_N)=0 , and \\theta=\\theta(t) \\quad \\forall t\\in[0,1) : a_{θ}(t) := \\dfrac{\\partial L}{\\partial θ(t)}, \\quad a_{t}(t) := \\dfrac{\\partial L}{\\partial t(t)} The corresponding dynamics can be similarly derived: \\dfrac{d a_{\\theta}(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} \\tag{5} \\dfrac{d a_t(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} \\tag{6} It leads to the following by integrating the both side: \\dfrac{\\partial L}{\\partial \\theta} = a_{θ}(t_0) = -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} dt \\tag{7} \\dfrac{\\partial L}{\\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} dt \\tag{8} And note that since a(t_N) is available, we can compute a_{t}(t_N) explicitly by using (1): a_{t}(t_N) = \\dfrac{\\partial L}{\\partial t_N} = \\dfrac{\\partial L}{\\partial z(t_N)} \\dfrac{\\partial z(t_N)}{\\partial t(t_N)} = a(t_N) f_\\theta(t_N,z(t_N)) We can now see that solving the equation (5) reversely via computing the integration of RHS in (7) leads to the desired approximated {\\partial L}/{\\partial \\theta} . Step 3: Compute the gradient via reverse ODE solving For reverse ODE solving, ODE solver can be used again. It should be applied to solve the following systems of the equations resulting from (1)~(8): \\begin{cases}\\dot z=f_\\theta(z,t), \\\\ \\dot a = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial z},\\\\ \\dot a_{\\theta} = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial \\theta},\\\\ \\dot a_t = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial t}. \\end{cases} where initial values are given as: \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} with \\dot{a} is abbrev. notation for da(t)/dt . Same notation for others. Why do we need to solve these many equations simultaneously? As can be checked in the formula, the desired dynamics for a_{\\theta} depends on a . Then the dynamics for a depends on z . So they need to be passed to the ODE solver simultaneously for proper computation. Note that the dynamics a_t(t) can be viewed as a just dummy for gradient computation process. But this post followed the original paper’s style. Now I believe that we can understand the above figure and the below algorithm presented in the original paper. Neural Ordinary Differential Equations (arxiv.org) Please refer to this code snippets for detailed implementation code: neural-ode/Neural ODEs.ipynb at master · msurtsukov/neural-ode (github.com) Application Modern Neural ODE The above process for approximating gradients may seem complicated and limited, but thanks to many, the ODE solver is no longer a black box. Differentiable ODE solvers are now available. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. We can now apply standard backpropagation in DL as usual. So, why introduce the earlier process? I believe that understanding the original Neural ODE derivation is valuable mathematical practice. And although differentiable ODE solvers are now available, they can’t be too heavy to apply to Neural ODEs. So, further breakthroughs are still needed. Normalizing Flow Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Normalizing flow is a generative model framework which involves an invertible function f between the data distribution space X and latent space Z . Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the x\\mapsto z direction and solving them in the z\\mapsto x direction for generation, known as continuous normalizing flow. This concept is related to the current flow-matching framework for diffusion models. Solving DEs Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently. Cases: Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Implicit Neural Representation (INR) uses NNs to model continuous functions that implicitly represent data, providing a flexible and efficient way to approximate complex physical phenomena. In other words, INR represents signals by continuous functions parameterized by NNs , unlike traditional discrete representations (e.g., pixel, mesh). This post introduces INR based on the following paper: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) Typical RGB image data \\Omega can be interpreted as a function: for spatial location x\\in \\Omega \\subset \\R^2 , it corresponds to a 3-dimensional RGB value f(x)\\in\\R^3 . Adversarial Generation of Continuous Images (arxiv.org) Given RGB image data \\Omega , INR is a coordinate-based NN that model data as the realization of an implicit function of a spatial location x ∈ Ω \\mapsto f_θ(x) where f_θ:\\R^2 \\rightarrow \\R^3 . It approximates f_\\theta \\approx f . Consider a 1024x1024x3 image. Based on int8 representation, this image would typically require 1024×1024×3=3,145,728 bytes of memory. And consider the above NN with 4 hidden layers of dimension 4 without bias term. Then first and last weight is W_0\\in \\R^{2\\times 4} and W_4\\in\\R^{4 \\times 3} respectively. And W_i\\in\\R^{4\\times 4} with i\\in\\{1,2,3\\} . The required memory to store the weights of this NN is 272 bytes only since there are 2x4+4x4x3+4x3=68 parameters, each needing 4 bytes (float32). If this NN can approximate the given image properly, it significantly reduces the required memory to store the data. This is a extreme example with exaggeration, but highlights the efficiency of INR. Continuous representation of INR can be obtained via physical on constraint of the data: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) The above figure demonstrates how INRs can leverage physical constraints to achieve continuous and high-fidelity image reconstruction and editing. Although the NN is fitted by physical constraints like gradient ( ∇f(x) ) and Laplacian ( Δf(x) ) instead of ground truth, the reconstructions closely match the original. This demonstrates that using a loss function based on physical constraints can efficiently approximate complex physical phenomena. Application: NERF NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) Perhaps NERF is the most famous example of an application of INRs. NERF creates continuous 5D scene representations using a Multi-Layer Perceptron (MLP) network. F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) The inputs are \\mathbf{x} = (x,y,z) , representing a 3D location and \\mathbf{d} , a 3D Cartesian unit vector. The outputs are the emitted RGB color \\mathbf{c} = (r,g,b) and volume density \\sigma . PINN PINNs use a loss function based on physical constraints of the given data, enabling the network to learn the data itself. This concept is fundamental to the idea of PINNs, where physical laws guide the learning process for more accurate and meaningful representations of complex phenomena. Overview From the previously introduced concepts, PINNs leverage the combination of physical constraints and data fitting to learn complex physical phenomena accurately. The main approach of PINNs can be expressed through the following loss function construction: \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} The content available at Physical Loss Terms — Physics-based Deep Learning provides good examples and illustrations, so some of them will be introduced here. Given a PDE for 𝑢(𝑥,𝑡) with a time evolution, we can typically express it in terms of a function \\mathcal F of the derivatives of 𝑢 via u_t = \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) \\tag{9} e.g. 1D Burgers Equation \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} Dataset for PINNs The datasets used for training PINNs are typically generated by simulating the dynamics of physical system. Dataset structure: D=\\{a_i, u_i\\}^N_{i=1} , where a_i=(x_i,t_i) represents the spatial and temporal variables, which are the inputs to the desired solution u . Specifically, u(a_i)=u_i . Burgers Equation Example: The equation (10) is known for modeling nonlinear wave propagation and shock wave formation. The data points (a_i,u_i) capture the evolution of the wave. HyPar: 1D Inviscid Burgers Equation - Sine Wave \\mathcal L_{data} : Supervised Learning (SL) Perspective Given the dataset D=\\{a_i, u_i\\}^N_{i=1} as above, it is natural to train the NN u_{\\theta} to approximate the true solution: u_{\\theta}(a_i)\\approx u_i . This objective leads to the SL loss: \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 The SL ensures that the function learned by the NN not only fits the data points but also satisfies the initial and boundary conditions of the problem. \\mathcal L_{physic} : INR Perspective Inspired by the introduced INR, PINN represents the loss function that encapsulates the physical constraints of the problem, also known as physic-informed loss . This term is crucial as it guides the neural network to learn solutions that not only fit the data but also comply with the underlying physical laws. The required physical constraint can be obtained from PDE (9). We want the residual R to be zero as a constraint: R \\coloneqq u_t - \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) =0 Concrete example: For the 1D Burgers equation (10), this leads to: R = \\dfrac{\\partial u}{\\partial t} + u \\dfrac{\\partial u}{\\partial x} - \\nu \\dfrac{\\partial^2 u}{\\partial x^2} The corresponding loss is: \\mathcal L_{physic}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert R(u_{\\theta}(a_i)) \\Vert^2 =\\dfrac{1}{N}\\sum_{i=1}^N \\Vert \\dfrac{\\partial u_{\\theta}(a_i)}{\\partial t} - \\mathcal{F}(\\dfrac{\\partial u_{\\theta}(a_i)}{\\partial x},\\cdots)\\Vert^2 Since u_{\\theta} is a continuous representation of the dynamics (i.e., INR), this kind of loss is possible. Total Framework: Physical Loss Terms — Physics-based Deep Learning (physicsbaseddeeplearning.org) Related Topics Theoretical Considerations: Not Generally Applicable to Any PDE: There are numerous reported failure modes when applying PINNs to different types of PDEs. Unstable Training: The training process for PINNs can be unstable, making it difficult to achieve convergence and reliable results across various problem settings. Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) presents the following theoretical results: When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. Alternative for Physical Constraint: Differentiable Numerical Simulations Introduction to Differentiable Physics — Physics-based Deep Learning (physicsbaseddeeplearning.org) These simulations offer a promising theoretical perspective, providing a robust framework for incorporating physical constraints directly into the learning process. Seems better in the theoretical perspective. But computationally infeasible in many cases. Relevant Resources: TORAX: google-deepmind/torax: TORAX: Tokamak transport simulation in JAX A differentiable tokamak core transport simulator designed for plasma physics research. torchdiffeq: rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. (github.com) Differentiable ODE solvers which can be combined with Neural ODEs as introduced above. Variations of PINNs: A Simple Example There are many variations of PINNs. Here's a simple one: Competitive Physics Informed Networks | OpenReview (ICLR 2023) The below figures and formulas are all from the original paper. Typical PDE Formulation: Typical PINN loss with NN \\mathcal P : \\mathcal L^{PINN}(\\mathcal P, x, \\bar{x}) = \\mathcal L^{PINN}_Ω(\\mathcal P, x) + \\mathcal L^{PINN}_{∂Ω}(\\mathcal P, \\bar{x}) The equation (4) is physic-informed loss and the equation (5) is supervised loss. Given the typical PINN framework as above, this paper proposes CPINNs which modifies this by introducing discriminator network D . Discriminator act as a point weight function following with a min-max optimization. It leads to the below objective: The equation (7) is a new physic-informed loss and the equation (8) is a new supervised loss. Neural Solver Towards Future of Simulation: Exploration Date: 08-09-2024 | Author: Ki-Ung Song Neural Solver Towards Future of Simulation: Exploration Date: 08-09-2024 | Author: Ki-Ung Song Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Intro Table of Contents Neural ODE Motivation Forward Computation Backward Computation Application PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Overview Related Topics Building on the foundational insights from our last post on neural solvers, we explore two main neural solver approaches in this post: Neural Ordinary Differential Equation (Neural ODE) Physics-Informed Neural Network (PINN) Neural ODE Motivation The literature presents numerous variants of Neural ODE. However, this post will be anchored in the foundational principles outlined in the original paper: Neural Ordinary Differential Equations (arxiv.org) Let’s start rethinking the residual connection: z_{t+1} = z_t + f(z_t, θ_t) where t ∈ \\{0, \\dots , T\\} , z_t ∈ \\R^D is hidden state of t -th layers, and \\theta_t is t -th layers weight. Recall the previously introduced Euler method: y_{n+1} = y_n + hf(t_n, y_n) By comparing this with the residual connection, we see a clear similarity: the residual connection can be interpreted as a single step of the Euler method with step-size h=1 . This allows transitioning from discrete layers to a continuous-time formulation, parameterizing the continuous dynamics between hidden states as evolving continuously over time: \\begin{align}\\dfrac{dz(t)}{dt} = f_θ(z(t), t) \\tag{1}\\end{align} Assume t\\in[0,1] . Note that this can also be interpreted as a NN with infinite depth. Forward Computation Now we have a sophisticated formulation for NN. But how can we compute the output of this DL model explicitly? While this continuous-time approach appears elegant, it's not immediately obvious how to calculate the model's output. Mathematical Solution: Given initial value z(t_0)=z(0) , the model output z(t_N)=z(1) is obtained via simply integrating the both side of (1): z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} But you should know that integration is not a “simple” process in the real world. Integration is only mathematically meaningful, but in reality, it often turns out to be a shiny but impractical concept. Actually, solving the differential equation is equivalent to approximating the integral in many cases. We can express the approximation of the RHS in (2) with ODESolve() operation. It leads to the following expression: z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) Assume that this kind of approximation is computed by black-box called “ODE solver”. ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used. Only two observations are possible: Input z_0 at the beginning t_0 and output z_1 at the end of the trajectory t_N since ODE solver is a black-box. Backward Computation But due to its black-box nature, typical gradient computation becomes infeasible . It means that {\\partial z(t_N)}/\\partial \\theta is not obtainable with the given z(t_N) , so we cannot apply chain rule to get {\\partial L}/{\\partial \\theta} for weight update. Then how can we actually train this kind of DL model? How do we apply back-propagation? The main idea to tackle this is to approximate the gradient {\\partial L}/{\\partial \\theta} for back-propagation. Let’s find out how this becomes possible. Before that we can typical assume supervised loss with the model’s output as: \\mathcal L(\\theta) \\approx L(z(t_N), y_{true}) \\eqqcolon L(z(t_N)) This is expressed as L(z(t_N)) = L \\big( \\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) \\big) Recall that our goal is to approximate the gradients of L with respect to its parameters \\theta . We achieve this through step-by-step computation of various quantities, as illustrated in the figure below. You may not understand this now, but it will become clear soon. Neural Ordinary Differential Equations (Neural ODEs): Rethinking Architecture | by Gwen Xiao | Medium Step 1: Focus on what we can compute and generalize it One quantity is straightforward to compute: \\dfrac{\\partial L}{\\partial z(t_N)} \\tag{*} Let’s generalize the above quantity to a component called adjoint a(t) : a(t) = \\dfrac{\\partial L}{\\partial z(t)} The above quantity (*) is now expressed as a component a(t_N) . Then the evolving dynamics of a(\\cdot) can be derived as follows \\dfrac{d a(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} \\tag{3} For derivation details, please refer to the original paper. This post will focus on providing a conceptual overview for better understanding. By applying integration to LHS of (3) and properly substituting with (3), the following can be obtained: a(t_0) = a(t_N) + \\int_{t_N}^{t_0} \\dfrac{da(t)}{dt} dt = a(t_N) - \\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} dt \\tag{4} What’s the meaning of this? The derived dynamics (3) is a form of ODE and from the last post, we know that the ODE can be solved reversed starting from a(t_N) . And it is equivalent to computing the integration in RHS of (4). From the discussion of previous section we can expect that the integration in RHS of (4) can be approximated via ODE solver. Step 2: Generalize again to other quantities We can define the following quantities assuming a_{θ}(t_N)=0 , and \\theta=\\theta(t) \\quad \\forall t\\in[0,1) : a_{θ}(t) := \\dfrac{\\partial L}{\\partial θ(t)}, \\quad a_{t}(t) := \\dfrac{\\partial L}{\\partial t(t)} The corresponding dynamics can be similarly derived: \\dfrac{d a_{\\theta}(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} \\tag{5} \\dfrac{d a_t(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} \\tag{6} It leads to the following by integrating the both side: \\dfrac{\\partial L}{\\partial \\theta} = a_{θ}(t_0) = -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} dt \\tag{7} \\dfrac{\\partial L}{\\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} dt \\tag{8} And note that since a(t_N) is available, we can compute a_{t}(t_N) explicitly by using (1): a_{t}(t_N) = \\dfrac{\\partial L}{\\partial t_N} = \\dfrac{\\partial L}{\\partial z(t_N)} \\dfrac{\\partial z(t_N)}{\\partial t(t_N)} = a(t_N) f_\\theta(t_N,z(t_N)) We can now see that solving the equation (5) reversely via computing the integration of RHS in (7) leads to the desired approximated {\\partial L}/{\\partial \\theta} . Step 3: Compute the gradient via reverse ODE solving For reverse ODE solving, ODE solver can be used again. It should be applied to solve the following systems of the equations resulting from (1)~(8): \\begin{cases}\\dot z=f_\\theta(z,t), \\\\ \\dot a = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial z},\\\\ \\dot a_{\\theta} = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial \\theta},\\\\ \\dot a_t = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial t}. \\end{cases} where initial values are given as: \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} with \\dot{a} is abbrev. notation for da(t)/dt . Same notation for others. Why do we need to solve these many equations simultaneously? As can be checked in the formula, the desired dynamics for a_{\\theta} depends on a . Then the dynamics for a depends on z . So they need to be passed to the ODE solver simultaneously for proper computation. Note that the dynamics a_t(t) can be viewed as a just dummy for gradient computation process. But this post followed the original paper’s style. Now I believe that we can understand the above figure and the below algorithm presented in the original paper. Neural Ordinary Differential Equations (arxiv.org) Please refer to this code snippets for detailed implementation code: neural-ode/Neural ODEs.ipynb at master · msurtsukov/neural-ode (github.com) Application Modern Neural ODE The above process for approximating gradients may seem complicated and limited, but thanks to many, the ODE solver is no longer a black box. Differentiable ODE solvers are now available. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. We can now apply standard backpropagation in DL as usual. So, why introduce the earlier process? I believe that understanding the original Neural ODE derivation is valuable mathematical practice. And although differentiable ODE solvers are now available, they can’t be too heavy to apply to Neural ODEs. So, further breakthroughs are still needed. Normalizing Flow Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Normalizing flow is a generative model framework which involves an invertible function f between the data distribution space X and latent space Z . Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the x\\mapsto z direction and solving them in the z\\mapsto x direction for generation, known as continuous normalizing flow. This concept is related to the current flow-matching framework for diffusion models. Solving DEs Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently. Cases: Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Implicit Neural Representation (INR) uses NNs to model continuous functions that implicitly represent data, providing a flexible and efficient way to approximate complex physical phenomena. In other words, INR represents signals by continuous functions parameterized by NNs , unlike traditional discrete representations (e.g., pixel, mesh). This post introduces INR based on the following paper: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) Typical RGB image data \\Omega can be interpreted as a function: for spatial location x\\in \\Omega \\subset \\R^2 , it corresponds to a 3-dimensional RGB value f(x)\\in\\R^3 . Adversarial Generation of Continuous Images (arxiv.org) Given RGB image data \\Omega , INR is a coordinate-based NN that model data as the realization of an implicit function of a spatial location x ∈ Ω \\mapsto f_θ(x) where f_θ:\\R^2 \\rightarrow \\R^3 . It approximates f_\\theta \\approx f . Consider a 1024x1024x3 image. Based on int8 representation, this image would typically require 1024×1024×3=3,145,728 bytes of memory. And consider the above NN with 4 hidden layers of dimension 4 without bias term. Then first and last weight is W_0\\in \\R^{2\\times 4} and W_4\\in\\R^{4 \\times 3} respectively. And W_i\\in\\R^{4\\times 4} with i\\in\\{1,2,3\\} . The required memory to store the weights of this NN is 272 bytes only since there are 2x4+4x4x3+4x3=68 parameters, each needing 4 bytes (float32). If this NN can approximate the given image properly, it significantly reduces the required memory to store the data. This is a extreme example with exaggeration, but highlights the efficiency of INR. Continuous representation of INR can be obtained via physical on constraint of the data: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) The above figure demonstrates how INRs can leverage physical constraints to achieve continuous and high-fidelity image reconstruction and editing. Although the NN is fitted by physical constraints like gradient ( ∇f(x) ) and Laplacian ( Δf(x) ) instead of ground truth, the reconstructions closely match the original. This demonstrates that using a loss function based on physical constraints can efficiently approximate complex physical phenomena. Application: NERF NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) Perhaps NERF is the most famous example of an application of INRs. NERF creates continuous 5D scene representations using a Multi-Layer Perceptron (MLP) network. F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) The inputs are \\mathbf{x} = (x,y,z) , representing a 3D location and \\mathbf{d} , a 3D Cartesian unit vector. The outputs are the emitted RGB color \\mathbf{c} = (r,g,b) and volume density \\sigma . PINN PINNs use a loss function based on physical constraints of the given data, enabling the network to learn the data itself. This concept is fundamental to the idea of PINNs, where physical laws guide the learning process for more accurate and meaningful representations of complex phenomena. Overview From the previously introduced concepts, PINNs leverage the combination of physical constraints and data fitting to learn complex physical phenomena accurately. The main approach of PINNs can be expressed through the following loss function construction: \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} The content available at Physical Loss Terms — Physics-based Deep Learning provides good examples and illustrations, so some of them will be introduced here. Given a PDE for 𝑢(𝑥,𝑡) with a time evolution, we can typically express it in terms of a function \\mathcal F of the derivatives of 𝑢 via u_t = \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) \\tag{9} e.g. 1D Burgers Equation \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} Dataset for PINNs The datasets used for training PINNs are typically generated by simulating the dynamics of physical system. Dataset structure: D=\\{a_i, u_i\\}^N_{i=1} , where a_i=(x_i,t_i) represents the spatial and temporal variables, which are the inputs to the desired solution u . Specifically, u(a_i)=u_i . Burgers Equation Example: The equation (10) is known for modeling nonlinear wave propagation and shock wave formation. The data points (a_i,u_i) capture the evolution of the wave. HyPar: 1D Inviscid Burgers Equation - Sine Wave \\mathcal L_{data} : Supervised Learning (SL) Perspective Given the dataset D=\\{a_i, u_i\\}^N_{i=1} as above, it is natural to train the NN u_{\\theta} to approximate the true solution: u_{\\theta}(a_i)\\approx u_i . This objective leads to the SL loss: \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 The SL ensures that the function learned by the NN not only fits the data points but also satisfies the initial and boundary conditions of the problem. \\mathcal L_{physic} : INR Perspective Inspired by the introduced INR, PINN represents the loss function that encapsulates the physical constraints of the problem, also known as physic-informed loss . This term is crucial as it guides the neural network to learn solutions that not only fit the data but also comply with the underlying physical laws. The required physical constraint can be obtained from PDE (9). We want the residual R to be zero as a constraint: R \\coloneqq u_t - \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) =0 Concrete example: For the 1D Burgers equation (10), this leads to: R = \\dfrac{\\partial u}{\\partial t} + u \\dfrac{\\partial u}{\\partial x} - \\nu \\dfrac{\\partial^2 u}{\\partial x^2} The corresponding loss is: \\mathcal L_{physic}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert R(u_{\\theta}(a_i)) \\Vert^2 =\\dfrac{1}{N}\\sum_{i=1}^N \\Vert \\dfrac{\\partial u_{\\theta}(a_i)}{\\partial t} - \\mathcal{F}(\\dfrac{\\partial u_{\\theta}(a_i)}{\\partial x},\\cdots)\\Vert^2 Since u_{\\theta} is a continuous representation of the dynamics (i.e., INR), this kind of loss is possible. Total Framework: Physical Loss Terms — Physics-based Deep Learning (physicsbaseddeeplearning.org) Related Topics Theoretical Considerations: Not Generally Applicable to Any PDE: There are numerous reported failure modes when applying PINNs to different types of PDEs. Unstable Training: The training process for PINNs can be unstable, making it difficult to achieve convergence and reliable results across various problem settings. Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) presents the following theoretical results: When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. Alternative for Physical Constraint: Differentiable Numerical Simulations Introduction to Differentiable Physics — Physics-based Deep Learning (physicsbaseddeeplearning.org) These simulations offer a promising theoretical perspective, providing a robust framework for incorporating physical constraints directly into the learning process. Seems better in the theoretical perspective. But computationally infeasible in many cases. Relevant Resources: TORAX: google-deepmind/torax: TORAX: Tokamak transport simulation in JAX A differentiable tokamak core transport simulator designed for plasma physics research. torchdiffeq: rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. (github.com) Differentiable ODE solvers which can be combined with Neural ODEs as introduced above. Variations of PINNs: A Simple Example There are many variations of PINNs. Here's a simple one: Competitive Physics Informed Networks | OpenReview (ICLR 2023) The below figures and formulas are all from the original paper. Typical PDE Formulation: Typical PINN loss with NN \\mathcal P : \\mathcal L^{PINN}(\\mathcal P, x, \\bar{x}) = \\mathcal L^{PINN}_Ω(\\mathcal P, x) + \\mathcal L^{PINN}_{∂Ω}(\\mathcal P, \\bar{x}) The equation (4) is physic-informed loss and the equation (5) is supervised loss. Given the typical PINN framework as above, this paper proposes CPINNs which modifies this by introducing discriminator network D . Discriminator act as a point weight function following with a min-max optimization. It leads to the below objective: The equation (7) is a new physic-informed loss and the equation (8) is a new supervised loss. Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Exploration - Current Post Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation: Intro Neural Solver Towards Future of Simulation: Intro Table of Contents Neural ODE Motivation Forward Computation Backward Computation Application PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Overview Related Topics Table of Contents Neural ODE Motivation Forward Computation Backward Computation Application PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Overview Related Topics Table of Contents Table of Contents Neural ODE Motivation Forward Computation Backward Computation Application PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Overview Related Topics Neural ODE Neural ODE Motivation Motivation Forward Computation Forward Computation Backward Computation Backward Computation Application Application PINN (Physics-Informed Neural Network) PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) INR ( Implicit Neural Representation) Overview Overview Related Topics Related Topics Building on the foundational insights from our last post on neural solvers, we explore two main neural solver approaches in this post: Neural Ordinary Differential Equation (Neural ODE) Neural Ordinary Differential Equation (Neural ODE) Neural Ordinary Differential Equation (Neural ODE) Physics-Informed Neural Network (PINN) Physics-Informed Neural Network (PINN) Physics-Informed Neural Network (PINN) Neural ODE Motivation The literature presents numerous variants of Neural ODE. However, this post will be anchored in the foundational principles outlined in the original paper: Neural Ordinary Differential Equations (arxiv.org) Neural Ordinary Differential Equations (arxiv.org) Let’s start rethinking the residual connection: z_{t+1} = z_t + f(z_t, θ_t) z_{t+1} = z_t + f(z_t, θ_t) z_{t+1} = z_t + f(z_t, θ_t) z_{t+1} = z_t + f(z_t, θ_t) where t ∈ \\{0, \\dots , T\\} , z_t ∈ \\R^D is hidden state of t -th layers, and \\theta_t is t -th layers weight. t ∈ \\{0, \\dots , T\\} t ∈ \\{0, \\dots , T\\} z_t ∈ \\R^D z_t ∈ \\R^D t t \\theta_t \\theta_t t t Recall the previously introduced Euler method: y_{n+1} = y_n + hf(t_n, y_n) y_{n+1} = y_n + hf(t_n, y_n) y_{n+1} = y_n + hf(t_n, y_n) y_{n+1} = y_n + hf(t_n, y_n) By comparing this with the residual connection, we see a clear similarity: the residual connection can be interpreted as a single step of the Euler method with step-size h=1 . h=1 h=1 This allows transitioning from discrete layers to a continuous-time formulation, parameterizing the continuous dynamics between hidden states as evolving continuously over time: \\begin{align}\\dfrac{dz(t)}{dt} = f_θ(z(t), t) \\tag{1}\\end{align} \\begin{align}\\dfrac{dz(t)}{dt} = f_θ(z(t), t) \\tag{1}\\end{align} \\begin{align}\\dfrac{dz(t)}{dt} = f_θ(z(t), t) \\tag{1}\\end{align} \\begin{align}\\dfrac{dz(t)}{dt} = f_θ(z(t), t) \\tag{1}\\end{align} Assume t\\in[0,1] . Assume t\\in[0,1] . t\\in[0,1] t\\in[0,1] Note that this can also be interpreted as a NN with infinite depth. Note that this can also be interpreted as a NN with infinite depth. Forward Computation Now we have a sophisticated formulation for NN. But how can we compute the output of this DL model explicitly? While this continuous-time approach appears elegant, it's not immediately obvious how to calculate the model's output. Mathematical Solution: Given initial value z(t_0)=z(0) , the model output z(t_N)=z(1) is obtained via simply integrating the both side of (1): z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} But you should know that integration is not a “simple” process in the real world. Integration is only mathematically meaningful, but in reality, it often turns out to be a shiny but impractical concept. Actually, solving the differential equation is equivalent to approximating the integral in many cases. We can express the approximation of the RHS in (2) with ODESolve() operation. It leads to the following expression: z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) Assume that this kind of approximation is computed by black-box called “ODE solver”. ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used. Only two observations are possible: Input z_0 at the beginning t_0 and output z_1 at the end of the trajectory t_N since ODE solver is a black-box. Mathematical Solution: Given initial value z(t_0)=z(0) , the model output z(t_N)=z(1) is obtained via simply integrating the both side of (1): z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} But you should know that integration is not a “simple” process in the real world. Integration is only mathematically meaningful, but in reality, it often turns out to be a shiny but impractical concept. Actually, solving the differential equation is equivalent to approximating the integral in many cases. We can express the approximation of the RHS in (2) with ODESolve() operation. It leads to the following expression: z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) Assume that this kind of approximation is computed by black-box called “ODE solver”. ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used. Only two observations are possible: Input z_0 at the beginning t_0 and output z_1 at the end of the trajectory t_N since ODE solver is a black-box. Given initial value z(t_0)=z(0) , the model output z(t_N)=z(1) is obtained via simply integrating the both side of (1): z(t_0)=z(0) z(t_0)=z(0) z(t_N)=z(1) z(t_N)=z(1) z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} z(t_N) = z(0) + \\int_{t_0}^{t_N} f_θ(z(t), t) \\ dt \\tag{2} But you should know that integration is not a “simple” process in the real world. Integration is only mathematically meaningful, but in reality, it often turns out to be a shiny but impractical concept. Actually, solving the differential equation is equivalent to approximating the integral in many cases. We can express the approximation of the RHS in (2) with ODESolve() operation. It leads to the following expression: ODESolve() z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) z(t_N)=\\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) Assume that this kind of approximation is computed by black-box called “ODE solver”. black-box ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used. ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used. Only two observations are possible: Input z_0 at the beginning t_0 and output z_1 at the end of the trajectory t_N since ODE solver is a black-box. Only two observations are possible: Input z_0 at the beginning t_0 and output z_1 at the end of the trajectory t_N since ODE solver is a black-box. z_0 z_0 t_0 t_0 z_1 z_1 t_N t_N Backward Computation But due to its black-box nature, typical gradient computation becomes infeasible . gradient computation becomes infeasible It means that {\\partial z(t_N)}/\\partial \\theta is not obtainable with the given z(t_N) , so we cannot apply chain rule to get {\\partial L}/{\\partial \\theta} for weight update. It means that {\\partial z(t_N)}/\\partial \\theta is not obtainable with the given z(t_N) , so we cannot apply chain rule to get {\\partial L}/{\\partial \\theta} for weight update. {\\partial z(t_N)}/\\partial \\theta {\\partial z(t_N)}/\\partial \\theta z(t_N) z(t_N) {\\partial L}/{\\partial \\theta} {\\partial L}/{\\partial \\theta} Then how can we actually train this kind of DL model? How do we apply back-propagation? The main idea to tackle this is to approximate the gradient {\\partial L}/{\\partial \\theta} for back-propagation. The main idea to tackle this is to approximate the gradient {\\partial L}/{\\partial \\theta} {\\partial L}/{\\partial \\theta} for back-propagation. Let’s find out how this becomes possible. Before that we can typical assume supervised loss with the model’s output as: \\mathcal L(\\theta) \\approx L(z(t_N), y_{true}) \\eqqcolon L(z(t_N)) \\mathcal L(\\theta) \\approx L(z(t_N), y_{true}) \\eqqcolon L(z(t_N)) \\mathcal L(\\theta) \\approx L(z(t_N), y_{true}) \\eqqcolon L(z(t_N)) \\mathcal L(\\theta) \\approx L(z(t_N), y_{true}) \\eqqcolon L(z(t_N)) This is expressed as L(z(t_N)) = L \\big( \\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) \\big) L(z(t_N)) = L \\big( \\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) \\big) L(z(t_N)) = L \\big( \\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) \\big) L(z(t_N)) = L \\big( \\text{ODESolve}(z(t_0), f_{\\theta}, t_0, t_N) \\big) Recall that our goal is to approximate the gradients of L with respect to its parameters \\theta . We achieve this through step-by-step computation of various quantities, as illustrated in the figure below. You may not understand this now, but it will become clear soon. L L \\theta \\theta Neural Ordinary Differential Equations (Neural ODEs): Rethinking Architecture | by Gwen Xiao | Medium Neural Ordinary Differential Equations (Neural ODEs): Rethinking Architecture | by Gwen Xiao | Medium Neural Ordinary Differential Equations (Neural ODEs): Rethinking Architecture | by Gwen Xiao | Medium Step 1: Focus on what we can compute and generalize it Step 1: Focus on what we can compute and generalize it One quantity is straightforward to compute: \\dfrac{\\partial L}{\\partial z(t_N)} \\tag{*} \\dfrac{\\partial L}{\\partial z(t_N)} \\tag{*} \\dfrac{\\partial L}{\\partial z(t_N)} \\tag{*} \\dfrac{\\partial L}{\\partial z(t_N)} \\tag{*} Let’s generalize the above quantity to a component called adjoint a(t) : adjoint a(t) a(t) a(t) = \\dfrac{\\partial L}{\\partial z(t)} a(t) = \\dfrac{\\partial L}{\\partial z(t)} a(t) = \\dfrac{\\partial L}{\\partial z(t)} a(t) = \\dfrac{\\partial L}{\\partial z(t)} The above quantity (*) is now expressed as a component a(t_N) . a(t_N) a(t_N) Then the evolving dynamics of a(\\cdot) can be derived as follows a(\\cdot) a(\\cdot) \\dfrac{d a(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} \\tag{3} \\dfrac{d a(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} \\tag{3} \\dfrac{d a(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} \\tag{3} \\dfrac{d a(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} \\tag{3} For derivation details, please refer to the original paper. This post will focus on providing a conceptual overview for better understanding. For derivation details, please refer to the original paper. This post will focus on providing a conceptual overview for better understanding. By applying integration to LHS of (3) and properly substituting with (3), the following can be obtained: a(t_0) = a(t_N) + \\int_{t_N}^{t_0} \\dfrac{da(t)}{dt} dt = a(t_N) - \\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} dt \\tag{4} a(t_0) = a(t_N) + \\int_{t_N}^{t_0} \\dfrac{da(t)}{dt} dt = a(t_N) - \\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} dt \\tag{4} a(t_0) = a(t_N) + \\int_{t_N}^{t_0} \\dfrac{da(t)}{dt} dt = a(t_N) - \\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} dt \\tag{4} a(t_0) = a(t_N) + \\int_{t_N}^{t_0} \\dfrac{da(t)}{dt} dt = a(t_N) - \\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial z(t)} dt \\tag{4} What’s the meaning of this? What’s the meaning of this? The derived dynamics (3) is a form of ODE and from the last post, we know that the ODE can be solved reversed starting from a(t_N) . And it is equivalent to computing the integration in RHS of (4). The derived dynamics (3) is a form of ODE and from the last post, we know that the ODE can be solved reversed starting from a(t_N) . And it is equivalent to computing the integration in RHS of (4). a(t_N) a(t_N) From the discussion of previous section we can expect that the integration in RHS of (4) can be approximated via ODE solver. From the discussion of previous section we can expect that the integration in RHS of (4) can be approximated via ODE solver. Step 2: Generalize again to other quantities Step 2: Generalize again to other quantities We can define the following quantities assuming a_{θ}(t_N)=0 , and \\theta=\\theta(t) \\quad \\forall t\\in[0,1) : a_{θ}(t_N)=0 a_{θ}(t_N)=0 \\theta=\\theta(t) \\quad \\forall t\\in[0,1) \\theta=\\theta(t) \\quad \\forall t\\in[0,1) a_{θ}(t) := \\dfrac{\\partial L}{\\partial θ(t)}, \\quad a_{t}(t) := \\dfrac{\\partial L}{\\partial t(t)} a_{θ}(t) := \\dfrac{\\partial L}{\\partial θ(t)}, \\quad a_{t}(t) := \\dfrac{\\partial L}{\\partial t(t)} a_{θ}(t) := \\dfrac{\\partial L}{\\partial θ(t)}, \\quad a_{t}(t) := \\dfrac{\\partial L}{\\partial t(t)} a_{θ}(t) := \\dfrac{\\partial L}{\\partial θ(t)}, \\quad a_{t}(t) := \\dfrac{\\partial L}{\\partial t(t)} The corresponding dynamics can be similarly derived: \\dfrac{d a_{\\theta}(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} \\tag{5} \\dfrac{d a_{\\theta}(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} \\tag{5} \\dfrac{d a_{\\theta}(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} \\tag{5} \\dfrac{d a_{\\theta}(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} \\tag{5} \\dfrac{d a_t(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} \\tag{6} \\dfrac{d a_t(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} \\tag{6} \\dfrac{d a_t(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} \\tag{6} \\dfrac{d a_t(t)}{d t} = -a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} \\tag{6} It leads to the following by integrating the both side: \\dfrac{\\partial L}{\\partial \\theta} = a_{θ}(t_0) = -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} dt \\tag{7} \\dfrac{\\partial L}{\\partial \\theta} = a_{θ}(t_0) = -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} dt \\tag{7} \\dfrac{\\partial L}{\\partial \\theta} = a_{θ}(t_0) = -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} dt \\tag{7} \\dfrac{\\partial L}{\\partial \\theta} = a_{θ}(t_0) = -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial \\theta} dt \\tag{7} \\dfrac{\\partial L}{\\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} dt \\tag{8} \\dfrac{\\partial L}{\\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} dt \\tag{8} \\dfrac{\\partial L}{\\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} dt \\tag{8} \\dfrac{\\partial L}{\\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\\int_{t_N}^{t_0} a(t) \\dfrac{\\partial f_\\theta(z(t), t)}{\\partial t} dt \\tag{8} And note that since a(t_N) is available, we can compute a_{t}(t_N) explicitly by using (1): a(t_N) a(t_N) a_{t}(t_N) a_{t}(t_N) a_{t}(t_N) = \\dfrac{\\partial L}{\\partial t_N} = \\dfrac{\\partial L}{\\partial z(t_N)} \\dfrac{\\partial z(t_N)}{\\partial t(t_N)} = a(t_N) f_\\theta(t_N,z(t_N)) a_{t}(t_N) = \\dfrac{\\partial L}{\\partial t_N} = \\dfrac{\\partial L}{\\partial z(t_N)} \\dfrac{\\partial z(t_N)}{\\partial t(t_N)} = a(t_N) f_\\theta(t_N,z(t_N)) a_{t}(t_N) = \\dfrac{\\partial L}{\\partial t_N} = \\dfrac{\\partial L}{\\partial z(t_N)} \\dfrac{\\partial z(t_N)}{\\partial t(t_N)} = a(t_N) f_\\theta(t_N,z(t_N)) a_{t}(t_N) = \\dfrac{\\partial L}{\\partial t_N} = \\dfrac{\\partial L}{\\partial z(t_N)} \\dfrac{\\partial z(t_N)}{\\partial t(t_N)} = a(t_N) f_\\theta(t_N,z(t_N)) We can now see that solving the equation (5) reversely via computing the integration of RHS in (7) leads to the desired approximated {\\partial L}/{\\partial \\theta} . {\\partial L}/{\\partial \\theta} {\\partial L}/{\\partial \\theta} Step 3: Compute the gradient via reverse ODE solving Step 3: Compute the gradient via reverse ODE solving For reverse ODE solving, ODE solver can be used again. It should be applied to solve the following systems of the equations resulting from (1)~(8): \\begin{cases}\\dot z=f_\\theta(z,t), \\\\ \\dot a = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial z},\\\\ \\dot a_{\\theta} = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial \\theta},\\\\ \\dot a_t = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial t}. \\end{cases} \\begin{cases}\\dot z=f_\\theta(z,t), \\\\ \\dot a = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial z},\\\\ \\dot a_{\\theta} = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial \\theta},\\\\ \\dot a_t = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial t}. \\end{cases} \\begin{cases}\\dot z=f_\\theta(z,t), \\\\ \\dot a = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial z},\\\\ \\dot a_{\\theta} = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial \\theta},\\\\ \\dot a_t = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial t}. \\end{cases} \\begin{cases}\\dot z=f_\\theta(z,t), \\\\ \\dot a = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial z},\\\\ \\dot a_{\\theta} = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial \\theta},\\\\ \\dot a_t = - a \\cdot \\dfrac{\\partial f_\\theta(z,t)}{\\partial t}. \\end{cases} where initial values are given as: \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} \\begin{cases} z(t_T)= \\text{Obtained at the forward pass}\\\\ a(t_N)=\\dfrac{\\partial L}{\\partial z(t_N)},\\\\ a_{θ}(t_N)=0,\\\\ a_t(t_N)=a(t_N) f_\\theta(t_N,z(t_N)). \\end{cases} with \\dot{a} is abbrev. notation for da(t)/dt . Same notation for others. \\dot{a} \\dot{a} da(t)/dt da(t)/dt Why do we need to solve these many equations simultaneously? As can be checked in the formula, the desired dynamics for a_{\\theta} depends on a . Then the dynamics for a depends on z . So they need to be passed to the ODE solver simultaneously for proper computation. Why do we need to solve these many equations simultaneously? As can be checked in the formula, the desired dynamics for a_{\\theta} depends on a . Then the dynamics for a depends on z . So they need to be passed to the ODE solver simultaneously for proper computation. Why do we need to solve these many equations simultaneously? As can be checked in the formula, the desired dynamics for a_{\\theta} depends on a . Then the dynamics for a depends on z . So they need to be passed to the ODE solver simultaneously for proper computation. a_{\\theta} a_{\\theta} a a a a z z Note that the dynamics a_t(t) can be viewed as a just dummy for gradient computation process. But this post followed the original paper’s style. Note that the dynamics a_t(t) can be viewed as a just dummy for gradient computation process. But this post followed the original paper’s style. a_t(t) a_t(t) Now I believe that we can understand the above figure and the below algorithm presented in the original paper. Neural Ordinary Differential Equations (arxiv.org) Neural Ordinary Differential Equations (arxiv.org) Neural Ordinary Differential Equations (arxiv.org) Please refer to this code snippets for detailed implementation code: neural-ode/Neural ODEs.ipynb at master · msurtsukov/neural-ode (github.com) neural-ode/Neural ODEs.ipynb at master · msurtsukov/neural-ode (github.com) Application Modern Neural ODE The above process for approximating gradients may seem complicated and limited, but thanks to many, the ODE solver is no longer a black box. Differentiable ODE solvers are now available. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. We can now apply standard backpropagation in DL as usual. So, why introduce the earlier process? I believe that understanding the original Neural ODE derivation is valuable mathematical practice. And although differentiable ODE solvers are now available, they can’t be too heavy to apply to Neural ODEs. So, further breakthroughs are still needed. Modern Neural ODE The above process for approximating gradients may seem complicated and limited, but thanks to many, the ODE solver is no longer a black box. Differentiable ODE solvers are now available. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. We can now apply standard backpropagation in DL as usual. So, why introduce the earlier process? I believe that understanding the original Neural ODE derivation is valuable mathematical practice. And although differentiable ODE solvers are now available, they can’t be too heavy to apply to Neural ODEs. So, further breakthroughs are still needed. Modern Neural ODE The above process for approximating gradients may seem complicated and limited, but thanks to many, the ODE solver is no longer a black box. Differentiable ODE solvers are now available. ODE solver is no longer a black box. Differentiable ODE solvers are now available. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. We can now apply standard backpropagation in DL as usual. So, why introduce the earlier process? I believe that understanding the original Neural ODE derivation is valuable mathematical practice. And although differentiable ODE solvers are now available, they can’t be too heavy to apply to Neural ODEs. So, further breakthroughs are still needed. Normalizing Flow Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Normalizing flow is a generative model framework which involves an invertible function f between the data distribution space X and latent space Z . Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the x\\mapsto z direction and solving them in the z\\mapsto x direction for generation, known as continuous normalizing flow. This concept is related to the current flow-matching framework for diffusion models. Normalizing Flow Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Normalizing flow is a generative model framework which involves an invertible function f between the data distribution space X and latent space Z . Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the x\\mapsto z direction and solving them in the z\\mapsto x direction for generation, known as continuous normalizing flow. This concept is related to the current flow-matching framework for diffusion models. Normalizing Flow Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io) Normalizing flow is a generative model framework which involves an invertible function f between the data distribution space X and latent space Z . Normalizing flow is a generative model framework which involves an invertible function f between the data distribution space X and latent space Z . f f X X Z Z Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the x\\mapsto z direction and solving them in the z\\mapsto x direction for generation, known as continuous normalizing flow. Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the x\\mapsto z direction and solving them in the z\\mapsto x direction for generation, known as continuous normalizing flow. x\\mapsto z x\\mapsto z z\\mapsto x z\\mapsto x continuous normalizing flow. This concept is related to the current flow-matching framework for diffusion models. This concept is related to the current flow-matching framework for diffusion models. Solving DEs Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently. Cases: Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. Solving DEs Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently. Cases: Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. Solving DEs Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently. Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently. Cases: Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. Cases: Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. Optical Flow Estimation: Captures dynamic motion in video sequences. Optical Flow Estimation: Captures dynamic motion in video sequences. Control Systems: Designs and analyzes stable control systems. Control Systems: Designs and analyzes stable control systems. Climate Modeling: Predicts and models climate dynamics. Climate Modeling: Predicts and models climate dynamics. PINN (Physics-Informed Neural Network) INR ( Implicit Neural Representation) Implicit Neural Representation (INR) uses NNs to model continuous functions that implicitly represent data, providing a flexible and efficient way to approximate complex physical phenomena. In other words, INR represents signals by continuous functions parameterized by NNs , unlike traditional discrete representations (e.g., pixel, mesh). In other words, INR represents signals by continuous functions parameterized by NNs , unlike traditional discrete representations (e.g., pixel, mesh). INR represents signals by continuous functions parameterized by NNs This post introduces INR based on the following paper: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) This post introduces INR based on the following paper: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) Implicit Neural Representations with Periodic Activation Functions (arxiv.org) Typical RGB image data \\Omega can be interpreted as a function: for spatial location x\\in \\Omega \\subset \\R^2 , it corresponds to a 3-dimensional RGB value f(x)\\in\\R^3 . \\Omega \\Omega x\\in \\Omega \\subset \\R^2 x\\in \\Omega \\subset \\R^2 f(x)\\in\\R^3 f(x)\\in\\R^3 Adversarial Generation of Continuous Images (arxiv.org) Adversarial Generation of Continuous Images (arxiv.org) Adversarial Generation of Continuous Images (arxiv.org) Given RGB image data \\Omega , INR is a coordinate-based NN that model data as the realization of an implicit function of a spatial location x ∈ Ω \\mapsto f_θ(x) where f_θ:\\R^2 \\rightarrow \\R^3 . It approximates f_\\theta \\approx f . \\Omega \\Omega x ∈ Ω \\mapsto f_θ(x) x ∈ Ω \\mapsto f_θ(x) f_θ:\\R^2 \\rightarrow \\R^3 f_θ:\\R^2 \\rightarrow \\R^3 f_\\theta \\approx f f_\\theta \\approx f Consider a 1024x1024x3 image. Based on int8 representation, this image would typically require 1024×1024×3=3,145,728 bytes of memory. Consider a 1024x1024x3 image. Based on int8 representation, this image would typically require 1024×1024×3=3,145,728 bytes of memory. And consider the above NN with 4 hidden layers of dimension 4 without bias term. Then first and last weight is W_0\\in \\R^{2\\times 4} and W_4\\in\\R^{4 \\times 3} respectively. And W_i\\in\\R^{4\\times 4} with i\\in\\{1,2,3\\} . The required memory to store the weights of this NN is 272 bytes only since there are 2x4+4x4x3+4x3=68 parameters, each needing 4 bytes (float32). And consider the above NN with 4 hidden layers of dimension 4 without bias term. Then first and last weight is W_0\\in \\R^{2\\times 4} and W_4\\in\\R^{4 \\times 3} respectively. And W_i\\in\\R^{4\\times 4} with i\\in\\{1,2,3\\} . The required memory to store the weights of this NN is 272 bytes only since there are 2x4+4x4x3+4x3=68 parameters, each needing 4 bytes (float32). W_0\\in \\R^{2\\times 4} W_0\\in \\R^{2\\times 4} W_4\\in\\R^{4 \\times 3} W_4\\in\\R^{4 \\times 3} W_i\\in\\R^{4\\times 4} W_i\\in\\R^{4\\times 4} i\\in\\{1,2,3\\} i\\in\\{1,2,3\\} If this NN can approximate the given image properly, it significantly reduces the required memory to store the data. This is a extreme example with exaggeration, but highlights the efficiency of INR. If this NN can approximate the given image properly, it significantly reduces the required memory to store the data. This is a extreme example with exaggeration, but highlights the efficiency of INR. Continuous representation of INR can be obtained via physical on constraint of the data: Continuous representation of INR can be obtained via physical on constraint of the data: Implicit Neural Representations with Periodic Activation Functions (arxiv.org) Implicit Neural Representations with Periodic Activation Functions (arxiv.org) Implicit Neural Representations with Periodic Activation Functions (arxiv.org) The above figure demonstrates how INRs can leverage physical constraints to achieve continuous and high-fidelity image reconstruction and editing. Although the NN is fitted by physical constraints like gradient ( ∇f(x) ) and Laplacian ( Δf(x) ) instead of ground truth, the reconstructions closely match the original. Although the NN is fitted by physical constraints like gradient ( ∇f(x) ) and Laplacian ( Δf(x) ) instead of ground truth, the reconstructions closely match the original. ∇f(x) ∇f(x) Δf(x) Δf(x) This demonstrates that using a loss function based on physical constraints can efficiently approximate complex physical phenomena. This demonstrates that using a loss function based on physical constraints can efficiently approximate complex physical phenomena. This demonstrates that using a loss function based on physical constraints can efficiently approximate complex physical phenomena. Application: Application: NERF NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) Perhaps NERF is the most famous example of an application of INRs. NERF creates continuous 5D scene representations using a Multi-Layer Perceptron (MLP) network. F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) The inputs are \\mathbf{x} = (x,y,z) , representing a 3D location and \\mathbf{d} , a 3D Cartesian unit vector. The outputs are the emitted RGB color \\mathbf{c} = (r,g,b) and volume density \\sigma . NERF NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) Perhaps NERF is the most famous example of an application of INRs. NERF creates continuous 5D scene representations using a Multi-Layer Perceptron (MLP) network. F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) The inputs are \\mathbf{x} = (x,y,z) , representing a 3D location and \\mathbf{d} , a 3D Cartesian unit vector. The outputs are the emitted RGB color \\mathbf{c} = (r,g,b) and volume density \\sigma . NERF NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org) Perhaps NERF is the most famous example of an application of INRs. NERF creates continuous 5D scene representations using a Multi-Layer Perceptron (MLP) network. F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) F_{\\theta} :(\\mathbf{x}, \\mathbf{d}) → (\\mathbf{c}, \\sigma) The inputs are \\mathbf{x} = (x,y,z) , representing a 3D location and \\mathbf{d} , a 3D Cartesian unit vector. The outputs are the emitted RGB color \\mathbf{c} = (r,g,b) and volume density \\sigma . \\mathbf{x} = (x,y,z) \\mathbf{x} = (x,y,z) \\mathbf{d} \\mathbf{d} \\mathbf{c} = (r,g,b) \\mathbf{c} = (r,g,b) \\sigma \\sigma PINN PINNs use a loss function based on physical constraints of the given data, enabling the network to learn the data itself. This concept is fundamental to the idea of PINNs, where physical laws guide the learning process for more accurate and meaningful representations of complex phenomena. PINN PINNs use a loss function based on physical constraints of the given data, enabling the network to learn the data itself. This concept is fundamental to the idea of PINNs, where physical laws guide the learning process for more accurate and meaningful representations of complex phenomena. PINN PINNs use a loss function based on physical constraints of the given data, enabling the network to learn the data itself. This concept is fundamental to the idea of PINNs, where physical laws guide the learning process for more accurate and meaningful representations of complex phenomena. Overview From the previously introduced concepts, PINNs leverage the combination of physical constraints and data fitting to learn complex physical phenomena accurately. The main approach of PINNs can be expressed through the following loss function construction: \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} The main approach of PINNs can be expressed through the following loss function construction: \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} \\mathcal L= \\mathcal L_{data} + \\lambda \\mathcal L_{physic} The content available at Physical Loss Terms — Physics-based Deep Learning provides good examples and illustrations, so some of them will be introduced here. Physical Loss Terms — Physics-based Deep Learning Given a PDE for 𝑢(𝑥,𝑡) with a time evolution, we can typically express it in terms of a function \\mathcal F of the derivatives of 𝑢 via 𝑢(𝑥,𝑡) 𝑢(𝑥,𝑡) \\mathcal F \\mathcal F 𝑢 𝑢 u_t = \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) \\tag{9} u_t = \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) \\tag{9} u_t = \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) \\tag{9} u_t = \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) \\tag{9} e.g. 1D Burgers Equation \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} e.g. 1D Burgers Equation \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} \\dfrac{\\partial u}{\\partial{t}} + u \\nabla u = \\nu \\nabla \\cdot \\nabla u \\tag{10} Dataset for PINNs Dataset for PINNs The datasets used for training PINNs are typically generated by simulating the dynamics of physical system. Dataset structure: D=\\{a_i, u_i\\}^N_{i=1} , where a_i=(x_i,t_i) represents the spatial and temporal variables, which are the inputs to the desired solution u . Specifically, u(a_i)=u_i . Dataset structure: D=\\{a_i, u_i\\}^N_{i=1} , where a_i=(x_i,t_i) represents the spatial and temporal variables, which are the inputs to the desired solution u . Specifically, u(a_i)=u_i . D=\\{a_i, u_i\\}^N_{i=1} D=\\{a_i, u_i\\}^N_{i=1} a_i=(x_i,t_i) a_i=(x_i,t_i) u u u(a_i)=u_i u(a_i)=u_i Burgers Equation Example: The equation (10) is known for modeling nonlinear wave propagation and shock wave formation. The data points (a_i,u_i) capture the evolution of the wave. HyPar: 1D Inviscid Burgers Equation - Sine Wave Burgers Equation Example: The equation (10) is known for modeling nonlinear wave propagation and shock wave formation. The data points (a_i,u_i) capture the evolution of the wave. HyPar: 1D Inviscid Burgers Equation - Sine Wave The equation (10) is known for modeling nonlinear wave propagation and shock wave formation. The data points (a_i,u_i) capture the evolution of the wave. (a_i,u_i) (a_i,u_i) HyPar: 1D Inviscid Burgers Equation - Sine Wave HyPar: 1D Inviscid Burgers Equation - Sine Wave HyPar: 1D Inviscid Burgers Equation - Sine Wave \\mathcal L_{data} : Supervised Learning (SL) Perspective \\mathcal L_{data} \\mathcal L_{data} : Supervised Learning (SL) Perspective Given the dataset D=\\{a_i, u_i\\}^N_{i=1} as above, it is natural to train the NN u_{\\theta} to approximate the true solution: u_{\\theta}(a_i)\\approx u_i . D=\\{a_i, u_i\\}^N_{i=1} D=\\{a_i, u_i\\}^N_{i=1} u_{\\theta} u_{\\theta} u_{\\theta}(a_i)\\approx u_i u_{\\theta}(a_i)\\approx u_i This objective leads to the SL loss: \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 This objective leads to the SL loss: \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 \\mathcal L_{data}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert u_{\\theta}(a_i)- u_i \\Vert^2 The SL ensures that the function learned by the NN not only fits the data points but also satisfies the initial and boundary conditions of the problem. The SL ensures that the function learned by the NN not only fits the data points but also satisfies the initial and boundary conditions of the problem. \\mathcal L_{physic} : INR Perspective \\mathcal L_{physic} \\mathcal L_{physic} : INR Perspective Inspired by the introduced INR, PINN represents the loss function that encapsulates the physical constraints of the problem, also known as physic-informed loss . physic-informed loss This term is crucial as it guides the neural network to learn solutions that not only fit the data but also comply with the underlying physical laws. The required physical constraint can be obtained from PDE (9). We want the residual R to be zero as a constraint: R R R \\coloneqq u_t - \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) =0 R \\coloneqq u_t - \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) =0 R \\coloneqq u_t - \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) =0 R \\coloneqq u_t - \\mathcal F (u_{x}, u_{xx}, \\cdots,u_{xx...x} ) =0 Concrete example: For the 1D Burgers equation (10), this leads to: Concrete example: For the 1D Burgers equation (10), this leads to: R = \\dfrac{\\partial u}{\\partial t} + u \\dfrac{\\partial u}{\\partial x} - \\nu \\dfrac{\\partial^2 u}{\\partial x^2} R = \\dfrac{\\partial u}{\\partial t} + u \\dfrac{\\partial u}{\\partial x} - \\nu \\dfrac{\\partial^2 u}{\\partial x^2} R = \\dfrac{\\partial u}{\\partial t} + u \\dfrac{\\partial u}{\\partial x} - \\nu \\dfrac{\\partial^2 u}{\\partial x^2} R = \\dfrac{\\partial u}{\\partial t} + u \\dfrac{\\partial u}{\\partial x} - \\nu \\dfrac{\\partial^2 u}{\\partial x^2} The corresponding loss is: \\mathcal L_{physic}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert R(u_{\\theta}(a_i)) \\Vert^2 =\\dfrac{1}{N}\\sum_{i=1}^N \\Vert \\dfrac{\\partial u_{\\theta}(a_i)}{\\partial t} - \\mathcal{F}(\\dfrac{\\partial u_{\\theta}(a_i)}{\\partial x},\\cdots)\\Vert^2 \\mathcal L_{physic}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert R(u_{\\theta}(a_i)) \\Vert^2 =\\dfrac{1}{N}\\sum_{i=1}^N \\Vert \\dfrac{\\partial u_{\\theta}(a_i)}{\\partial t} - \\mathcal{F}(\\dfrac{\\partial u_{\\theta}(a_i)}{\\partial x},\\cdots)\\Vert^2 \\mathcal L_{physic}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert R(u_{\\theta}(a_i)) \\Vert^2 =\\dfrac{1}{N}\\sum_{i=1}^N \\Vert \\dfrac{\\partial u_{\\theta}(a_i)}{\\partial t} - \\mathcal{F}(\\dfrac{\\partial u_{\\theta}(a_i)}{\\partial x},\\cdots)\\Vert^2 \\mathcal L_{physic}=\\dfrac{1}{N}\\sum_{i=1}^N \\Vert R(u_{\\theta}(a_i)) \\Vert^2 =\\dfrac{1}{N}\\sum_{i=1}^N \\Vert \\dfrac{\\partial u_{\\theta}(a_i)}{\\partial t} - \\mathcal{F}(\\dfrac{\\partial u_{\\theta}(a_i)}{\\partial x},\\cdots)\\Vert^2 Since u_{\\theta} is a continuous representation of the dynamics (i.e., INR), this kind of loss is possible. u_{\\theta} u_{\\theta} Total Framework: Total Framework: Physical Loss Terms — Physics-based Deep Learning (physicsbaseddeeplearning.org) Physical Loss Terms — Physics-based Deep Learning (physicsbaseddeeplearning.org) Physical Loss Terms — Physics-based Deep Learning (physicsbaseddeeplearning.org) Related Topics Theoretical Considerations: Theoretical Considerations: Not Generally Applicable to Any PDE: There are numerous reported failure modes when applying PINNs to different types of PDEs. Not Generally Applicable to Any PDE: There are numerous reported failure modes when applying PINNs to different types of PDEs. Not Generally Applicable to Any PDE: Unstable Training: The training process for PINNs can be unstable, making it difficult to achieve convergence and reliable results across various problem settings. Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) presents the following theoretical results: When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. Unstable Training: The training process for PINNs can be unstable, making it difficult to achieve convergence and reliable results across various problem settings. Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) presents the following theoretical results: When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. Unstable Training: Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) presents the following theoretical results: When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) presents the following theoretical results: When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network? (NeurIPS 2022) When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. When p is small, such as p=2 , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs. p p p=2 p=2 This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. This implies that the conventional choice of L^2 norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs. L^2 L^2 Alternative for Physical Constraint: Differentiable Numerical Simulations Alternative for Physical Constraint: Differentiable Numerical Simulations Introduction to Differentiable Physics — Physics-based Deep Learning (physicsbaseddeeplearning.org) Introduction to Differentiable Physics — Physics-based Deep Learning (physicsbaseddeeplearning.org) Introduction to Differentiable Physics — Physics-based Deep Learning (physicsbaseddeeplearning.org) These simulations offer a promising theoretical perspective, providing a robust framework for incorporating physical constraints directly into the learning process. Seems better in the theoretical perspective. But computationally infeasible in many cases. Relevant Resources: TORAX: google-deepmind/torax: TORAX: Tokamak transport simulation in JAX A differentiable tokamak core transport simulator designed for plasma physics research. TORAX: google-deepmind/torax: TORAX: Tokamak transport simulation in JAX A differentiable tokamak core transport simulator designed for plasma physics research. google-deepmind/torax: TORAX: Tokamak transport simulation in JAX A differentiable tokamak core transport simulator designed for plasma physics research. A differentiable tokamak core transport simulator designed for plasma physics research. torchdiffeq: rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. (github.com) Differentiable ODE solvers which can be combined with Neural ODEs as introduced above. torchdiffeq: rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. (github.com) Differentiable ODE solvers which can be combined with Neural ODEs as introduced above. rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. (github.com) Differentiable ODE solvers which can be combined with Neural ODEs as introduced above. Differentiable ODE solvers which can be combined with Neural ODEs as introduced above. Variations of PINNs: A Simple Example Variations of PINNs: A Simple Example There are many variations of PINNs. Here's a simple one: Competitive Physics Informed Networks | OpenReview (ICLR 2023) Competitive Physics Informed Networks | OpenReview (ICLR 2023) Competitive Physics Informed Networks | OpenReview Competitive Physics Informed Networks | OpenReview (ICLR 2023) The below figures and formulas are all from the original paper. The below figures and formulas are all from the original paper. Typical PDE Formulation: Typical PINN loss with NN \\mathcal P : \\mathcal P \\mathcal P \\mathcal L^{PINN}(\\mathcal P, x, \\bar{x}) = \\mathcal L^{PINN}_Ω(\\mathcal P, x) + \\mathcal L^{PINN}_{∂Ω}(\\mathcal P, \\bar{x}) \\mathcal L^{PINN}(\\mathcal P, x, \\bar{x}) = \\mathcal L^{PINN}_Ω(\\mathcal P, x) + \\mathcal L^{PINN}_{∂Ω}(\\mathcal P, \\bar{x}) \\mathcal L^{PINN}(\\mathcal P, x, \\bar{x}) = \\mathcal L^{PINN}_Ω(\\mathcal P, x) + \\mathcal L^{PINN}_{∂Ω}(\\mathcal P, \\bar{x}) \\mathcal L^{PINN}(\\mathcal P, x, \\bar{x}) = \\mathcal L^{PINN}_Ω(\\mathcal P, x) + \\mathcal L^{PINN}_{∂Ω}(\\mathcal P, \\bar{x}) The equation (4) is physic-informed loss and the equation (5) is supervised loss. The equation (4) is physic-informed loss and the equation (5) is supervised loss. Given the typical PINN framework as above, this paper proposes CPINNs which modifies this by introducing discriminator network D . proposes CPINNs which modifies this by introducing discriminator network D D Discriminator act as a point weight function following with a min-max optimization. It leads to the below objective: Discriminator act as a point weight function The equation (7) is a new physic-informed loss and the equation (8) is a new supervised loss. The equation (7) is a new physic-informed loss and the equation (8) is a new supervised loss."}, {"id": "001_<PERSON><PERSON><PERSON>_Solver_Towards_Future_of_Simulation_Intro", "title": "Neural Solver Towards Future of Simulation: Intro", "path": "pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content.html", "date": "08-09-2024", "tags": ["<PERSON><PERSON><PERSON>", "Simulation"], "content": "Neural Solver Towards Future of Simulation: Intro Date: 08-09-2024 | Author: <PERSON><PERSON><PERSON><PERSON> Song Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro - Current Post Table of Contents What is Simulation? Why Simulation? How Simulation Works? How to Solve Differential Equations? Neural Solver: DL for Solving Differential Equation Vanilla Approach Next Steps in Advancing Neural Solvers What is Simulation? 💡 \"We Are Most Likely in a Simulation\" -<PERSON><PERSON> Musk- Everyone has likely heard the term \"simulation.” But what exactly is a simulation? And how does this concept, as <PERSON><PERSON> suggests, challenge our understanding of reality and our existence? Wikipedia describes a simulation as follows. A simulation is an imitative representation of a process or system that could exist in the real world. - Wikipedia - So, how closely can a simulation mimic reality? With the advent of LLMs, we may think of the following scenarios when we hear “simulation”. Generative Agents: Interactive Simulacra of Human Behavior (arxiv.org) From <PERSON><PERSON>'s perspective, could our existence be just like the world as above, where each of us acts as objects with individual system prompts given as inputs? Simulation modeling like the above example is called Agent-Based Modeling (ABM) . ABM provides valuable insights into economic and environmental systems: Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules. These simulations are useful for studying emergent complex outcomes from simple underlying behaviors. ABM is an interesting topic, but today we will focus on traditional simulation methods. They can surprisingly mimic part of our reality and are widely used to solve a variety of real-world problems. Why Simulation? Paradigm Shift of Science Machine learning in concrete science: applications, challenges, and best practices Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. How DL can help? The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. How Simulation Works? The existing simulation involves creating a mathematical model that approximates real-world processes, often relying on solving various types of differential equations (DEs): ordinary (ODEs), partial (PDEs), and stochastic (SDEs). These equations are pivotal in modeling everything from dynamic physical systems and fluid dynamics to financial models and biological processes. Here are some detailed examples: Computer Graphics: Frozen In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior: A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life: Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) For those who are interested in the PDEs, click the toggle below for details. Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following: The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering Computational Fluid Dynamics (CFD): Car Design What is computational fluid dynamics? - Quora In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability. Molecular Dynamics (MD): Drug Discovery BioSimLab - Research (uark.edu) In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs. Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above. As seen in the examples above, simulations based on equations help solve problems by mimicking real-world phenomena in various fields. The naturally following question is how to solve these differential equations. How to Solve Differential Equations? Finite Difference Method (FDM) FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics. The basic idea behind FDM is to replace continuous derivatives with discrete approximations: \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} Euler Method The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs. Given the following simple form of ODE \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . Solving ODEs in reverse will be important in future content. Please remember. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Finite Element Method (FEM) FEM is another commonly used method to solve various DEs. t divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method. Example of a mesh grid in FEM: Finite element method - Wikipedia FEM aims to approximate the exact solution u of given PDE as a linear combination of basis (trial) functions \\{\\phi_i\\}_{i=1}^N : u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. What do basis functions look like? These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) The FEM method can be used with the FDM to address more complex and time-dependent PDEs. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. 💡 Summarizing the content so far, the direction towards better simulation using DL ultimately leads to utilizing DL for improved differential equation solving. Neural Solver: DL for Solving Differential Equation Neural solvers are DL-based frameworks designed to solve differential equations efficiently and accurately. By training and utilizing existing data from these equations, they aim to predict solutions to complex problems that are computationally intensive for traditional methods. Trend of Accepted Papers In recent years, the number of accepted papers on neural solvers has significantly increased. It reflects a growing interest within the scientific community. This trend highlights the potential of DL in transforming traditional problem-solving methods in mathematics and engineering. 2021 2022 2023 2024 NeurIPS 11 17 33 - ICLR 3 6 11 9 ICML 3 3 21 17 The table is based on a survey conducted as of July 10, 2024, so it does not reflect status regarding NeurIPS 2024. Vanilla Approach Why don't we apply DL to the simulation data directly then? Shouldn't it work? The content available at Supervised training for RANS flows around airfoils — Physics-based Deep Learning provides an excellent example of this and so it will be introduced here. Training data: Reynolds-Averaged Navier Stokes(RANS) around Airfoils Supervised training for RANS flows around airfoils — Physics-based Deep Learning RANS approach is a fundamental method used in computational fluid dynamics for simulation. Underlying dynamics: Navier-Stokes Equation \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. DL model: Simple U-net shape architecture with naive supervised loss Supervised training for RANS flows around airfoils — Physics-based Deep Learning \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 where x_i are the boundary conditions shown on the left side of the figure, and the target y_i are the pre-computed field quantities like pressure and velocity on the right side. Results: Not Optimal Supervised training for RANS flows around airfoils — Physics-based Deep Learning The vanilla DL approach shows limitations in capturing the complex dynamics of around airfoils, with predictions often not aligning closely with physical data, suggesting a need for more advanced models or training methods. Next Steps in Advancing Neural Solvers In the upcoming posts, we will explore innovative neural solver methodologies that promise greater fidelity and robustness to address the challenges shown in vanilla DL approach. Specifically, three advanced methods will be introduced: Neural Ordinary Differential Equations (NeuralODE) , Physics-Informed Neural Networks (PINN) , and Neural Operators . NeuralODE represents a fusion of deep learning with differential equations, offering a continuous-time model that can dynamically adapt to the underlying physics. PINN incorporates physical laws into the neural network’s loss function, enhancing prediction accuracy and model generalizability. Neural Operators learn mappings in infinite-dimensional spaces, enabling broad applicability across different geometries and conditions. These methods not only address traditional DL model limitations but also offer new ways to simulate complex physical systems with high precision. The upcoming post will delve into a detailed examination of each technique, their potential, and their applications. Neural Solver Towards Future of Simulation: Intro Date: 08-09-2024 | Author: Ki-Ung Song Neural Solver Towards Future of Simulation: Intro Date: 08-09-2024 | Author: Ki-Ung Song Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro - Current Post Table of Contents What is Simulation? Why Simulation? How Simulation Works? How to Solve Differential Equations? Neural Solver: DL for Solving Differential Equation Vanilla Approach Next Steps in Advancing Neural Solvers What is Simulation? 💡 \"We Are Most Likely in a Simulation\" -Elon Musk- Everyone has likely heard the term \"simulation.” But what exactly is a simulation? And how does this concept, as Elon suggests, challenge our understanding of reality and our existence? Wikipedia describes a simulation as follows. A simulation is an imitative representation of a process or system that could exist in the real world. - Wikipedia - So, how closely can a simulation mimic reality? With the advent of LLMs, we may think of the following scenarios when we hear “simulation”. Generative Agents: Interactive Simulacra of Human Behavior (arxiv.org) From Elon's perspective, could our existence be just like the world as above, where each of us acts as objects with individual system prompts given as inputs? Simulation modeling like the above example is called Agent-Based Modeling (ABM) . ABM provides valuable insights into economic and environmental systems: Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules. These simulations are useful for studying emergent complex outcomes from simple underlying behaviors. ABM is an interesting topic, but today we will focus on traditional simulation methods. They can surprisingly mimic part of our reality and are widely used to solve a variety of real-world problems. Why Simulation? Paradigm Shift of Science Machine learning in concrete science: applications, challenges, and best practices Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. How DL can help? The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. How Simulation Works? The existing simulation involves creating a mathematical model that approximates real-world processes, often relying on solving various types of differential equations (DEs): ordinary (ODEs), partial (PDEs), and stochastic (SDEs). These equations are pivotal in modeling everything from dynamic physical systems and fluid dynamics to financial models and biological processes. Here are some detailed examples: Computer Graphics: Frozen In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior: A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life: Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) For those who are interested in the PDEs, click the toggle below for details. Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following: The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering Computational Fluid Dynamics (CFD): Car Design What is computational fluid dynamics? - Quora In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability. Molecular Dynamics (MD): Drug Discovery BioSimLab - Research (uark.edu) In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs. Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above. As seen in the examples above, simulations based on equations help solve problems by mimicking real-world phenomena in various fields. The naturally following question is how to solve these differential equations. How to Solve Differential Equations? Finite Difference Method (FDM) FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics. The basic idea behind FDM is to replace continuous derivatives with discrete approximations: \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} Euler Method The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs. Given the following simple form of ODE \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . Solving ODEs in reverse will be important in future content. Please remember. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Finite Element Method (FEM) FEM is another commonly used method to solve various DEs. t divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method. Example of a mesh grid in FEM: Finite element method - Wikipedia FEM aims to approximate the exact solution u of given PDE as a linear combination of basis (trial) functions \\{\\phi_i\\}_{i=1}^N : u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. What do basis functions look like? These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) The FEM method can be used with the FDM to address more complex and time-dependent PDEs. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. 💡 Summarizing the content so far, the direction towards better simulation using DL ultimately leads to utilizing DL for improved differential equation solving. Neural Solver: DL for Solving Differential Equation Neural solvers are DL-based frameworks designed to solve differential equations efficiently and accurately. By training and utilizing existing data from these equations, they aim to predict solutions to complex problems that are computationally intensive for traditional methods. Trend of Accepted Papers In recent years, the number of accepted papers on neural solvers has significantly increased. It reflects a growing interest within the scientific community. This trend highlights the potential of DL in transforming traditional problem-solving methods in mathematics and engineering. 2021 2022 2023 2024 NeurIPS 11 17 33 - ICLR 3 6 11 9 ICML 3 3 21 17 The table is based on a survey conducted as of July 10, 2024, so it does not reflect status regarding NeurIPS 2024. Vanilla Approach Why don't we apply DL to the simulation data directly then? Shouldn't it work? The content available at Supervised training for RANS flows around airfoils — Physics-based Deep Learning provides an excellent example of this and so it will be introduced here. Training data: Reynolds-Averaged Navier Stokes(RANS) around Airfoils Supervised training for RANS flows around airfoils — Physics-based Deep Learning RANS approach is a fundamental method used in computational fluid dynamics for simulation. Underlying dynamics: Navier-Stokes Equation \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. DL model: Simple U-net shape architecture with naive supervised loss Supervised training for RANS flows around airfoils — Physics-based Deep Learning \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 where x_i are the boundary conditions shown on the left side of the figure, and the target y_i are the pre-computed field quantities like pressure and velocity on the right side. Results: Not Optimal Supervised training for RANS flows around airfoils — Physics-based Deep Learning The vanilla DL approach shows limitations in capturing the complex dynamics of around airfoils, with predictions often not aligning closely with physical data, suggesting a need for more advanced models or training methods. Next Steps in Advancing Neural Solvers In the upcoming posts, we will explore innovative neural solver methodologies that promise greater fidelity and robustness to address the challenges shown in vanilla DL approach. Specifically, three advanced methods will be introduced: Neural Ordinary Differential Equations (NeuralODE) , Physics-Informed Neural Networks (PINN) , and Neural Operators . NeuralODE represents a fusion of deep learning with differential equations, offering a continuous-time model that can dynamically adapt to the underlying physics. PINN incorporates physical laws into the neural network’s loss function, enhancing prediction accuracy and model generalizability. Neural Operators learn mappings in infinite-dimensional spaces, enabling broad applicability across different geometries and conditions. These methods not only address traditional DL model limitations but also offer new ways to simulate complex physical systems with high precision. The upcoming post will delve into a detailed examination of each technique, their potential, and their applications. Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro - Current Post Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation Series Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro - Current Post Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Deep Dive Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Exploration Neural Solver Towards Future of Simulation: Intro - Current Post Neural Solver Towards Future of Simulation: Intro - Current Post Neural Solver Towards Future of Simulation: Intro - Current Post Table of Contents What is Simulation? Why Simulation? How Simulation Works? How to Solve Differential Equations? Neural Solver: DL for Solving Differential Equation Vanilla Approach Next Steps in Advancing Neural Solvers Table of Contents What is Simulation? Why Simulation? How Simulation Works? How to Solve Differential Equations? Neural Solver: DL for Solving Differential Equation Vanilla Approach Next Steps in Advancing Neural Solvers Table of Contents Table of Contents What is Simulation? Why Simulation? How Simulation Works? How to Solve Differential Equations? Neural Solver: DL for Solving Differential Equation Vanilla Approach Next Steps in Advancing Neural Solvers What is Simulation? What is Simulation? Why Simulation? Why Simulation? How Simulation Works? How Simulation Works? How to Solve Differential Equations? How to Solve Differential Equations? Neural Solver: DL for Solving Differential Equation Neural Solver: DL for Solving Differential Equation Vanilla Approach Vanilla Approach Next Steps in Advancing Neural Solvers Next Steps in Advancing Neural Solvers What is Simulation? 💡 \"We Are Most Likely in a Simulation\" -Elon Musk- 💡 💡 \"We Are Most Likely in a Simulation\" -Elon Musk- \"We Are Most Likely in a Simulation\" -Elon Musk- Everyone has likely heard the term \"simulation.” But what exactly is a simulation? And how does this concept, as Elon suggests, challenge our understanding of reality and our existence? Wikipedia describes a simulation as follows. A simulation is an imitative representation of a process or system that could exist in the real world. - Wikipedia - simulation Wikipedia So, how closely can a simulation mimic reality? With the advent of LLMs, we may think of the following scenarios when we hear “simulation”. Generative Agents: Interactive Simulacra of Human Behavior (arxiv.org) Generative Agents: Interactive Simulacra of Human Behavior (arxiv.org) Generative Agents: Interactive Simulacra of Human Behavior (arxiv.org) From Elon's perspective, could our existence be just like the world as above, where each of us acts as objects with individual system prompts given as inputs? From Elon's perspective, could our existence be just like the world as above, where each of us acts as objects with individual system prompts given as inputs? Simulation modeling like the above example is called Agent-Based Modeling (ABM) . Simulation modeling like the above example is called Agent-Based Modeling (ABM) . Agent-Based Modeling (ABM) ABM provides valuable insights into economic and environmental systems: Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules. These simulations are useful for studying emergent complex outcomes from simple underlying behaviors. ABM provides valuable insights into economic and environmental systems: Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules. These simulations are useful for studying emergent complex outcomes from simple underlying behaviors. Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules. Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules. These simulations are useful for studying emergent complex outcomes from simple underlying behaviors. These simulations are useful for studying emergent complex outcomes from simple underlying behaviors. ABM is an interesting topic, but today we will focus on traditional simulation methods. They can surprisingly mimic part of our reality and are widely used to solve a variety of real-world problems. Why Simulation? Paradigm Shift of Science Machine learning in concrete science: applications, challenges, and best practices Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. Paradigm Shift of Science Machine learning in concrete science: applications, challenges, and best practices Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. Paradigm Shift of Science Machine learning in concrete science: applications, challenges, and best practices Machine learning in concrete science: applications, challenges, and best practices Machine learning in concrete science: applications, challenges, and best practices Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science. Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm. New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post. How DL can help? The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. How DL can help? The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. How DL can help? The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development. The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data. How Simulation Works? The existing simulation involves creating a mathematical model that approximates real-world processes, often relying on solving various types of differential equations (DEs): ordinary (ODEs), partial (PDEs), and stochastic (SDEs). These equations are pivotal in modeling everything from dynamic physical systems and fluid dynamics to financial models and biological processes. Here are some detailed examples: Computer Graphics: Frozen In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior: A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life: Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) For those who are interested in the PDEs, click the toggle below for details. Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following: The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering Computer Graphics: Frozen In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior: A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life: Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) For those who are interested in the PDEs, click the toggle below for details. Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following: The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering Computer Graphics: Frozen In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior: A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior: A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T A Material Point Method For Snow Simulation (disneyanimation.com) \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T \\dfrac{D\\rho}{Dt}=0, \\quad \\rho \\dfrac{D \\bm{v}}{Dt} = \\nabla \\cdot \\bm{\\sigma} + \\rho \\bm{g}, \\quad \\bm{\\sigma} = \\dfrac{1}{J}\\dfrac{\\partial \\Psi}{\\partial \\bm{F}_E} \\bm{F}_E^T These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life: Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life: Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) Disney's Frozen A Material Point Method For Snow Simulation (youtube.com) For those who are interested in the PDEs, click the toggle below for details. Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . For those who are interested in the PDEs, click the toggle below for details. Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . Details Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . Snow body’s deformation can be described as a mapping from its undeformed configuration \\bm{X} to its deformed configuration \\bm{x} by \\bm{x} = φ(\\bm{X}) , which yields the deformation gradient \\bm{F} = ∂φ/∂\\bm{X} . \\bm{X} \\bm{X} \\bm{x} \\bm{x} \\bm{x} = φ(\\bm{X}) \\bm{x} = φ(\\bm{X}) \\bm{F} = ∂φ/∂\\bm{X} \\bm{F} = ∂φ/∂\\bm{X} Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. Deformation φ(\\bm{X}) changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation. φ(\\bm{X}) φ(\\bm{X}) ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . ρ is density, t is time, \\bm{v} is velocity, \\bm{σ} is the Cauchy stress, \\bm{g} is the gravity, Ψ is the elasto-plastic potential energy density, \\bm{F}_E is the elastic part of the deformation gradient \\bm{F} and J = \\det(\\bm{F}) . ρ ρ t t \\bm{v} \\bm{v} \\bm{σ} \\bm{σ} \\bm{g} \\bm{g} Ψ Ψ \\bm{F}_E \\bm{F}_E \\bm{F} \\bm{F} J = \\det(\\bm{F}) J = \\det(\\bm{F}) The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient. Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . Specifically, each particle p holds position \\bm{x}_p=(x_p, y_p, z_p) , velocity \\bm{v}_p , mass m_p , and deformation gradient \\bm{F}_p . p p \\bm{x}_p=(x_p, y_p, z_p) \\bm{x}_p=(x_p, y_p, z_p) \\bm{v}_p \\bm{v}_p m_p m_p \\bm{F}_p \\bm{F}_p More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following: The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following: The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering Computational Fluid Dynamics (CFD): Car Design What is computational fluid dynamics? - Quora In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability. Computational Fluid Dynamics (CFD): Car Design What is computational fluid dynamics? - Quora In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability. Computational Fluid Dynamics (CFD): Car Design What is computational fluid dynamics? - Quora What is computational fluid dynamics? - Quora What is computational fluid dynamics? - Quora In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability. In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability. Molecular Dynamics (MD): Drug Discovery BioSimLab - Research (uark.edu) In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs. Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above. Molecular Dynamics (MD): Drug Discovery BioSimLab - Research (uark.edu) In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs. Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above. Molecular Dynamics (MD): Drug Discovery BioSimLab - Research (uark.edu) BioSimLab - Research (uark.edu) BioSimLab - Research (uark.edu) In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs. In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs. Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above. Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above. As seen in the examples above, simulations based on equations help solve problems by mimicking real-world phenomena in various fields. The naturally following question is how to solve these differential equations. As seen in the examples above, simulations based on equations help solve problems by mimicking real-world phenomena in various fields. The naturally following question is how to solve these differential equations. How to Solve Differential Equations? Finite Difference Method (FDM) FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics. The basic idea behind FDM is to replace continuous derivatives with discrete approximations: \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} Finite Difference Method (FDM) FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics. The basic idea behind FDM is to replace continuous derivatives with discrete approximations: \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} Finite Difference Method (FDM) FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics. FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics. The basic idea behind FDM is to replace continuous derivatives with discrete approximations: \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} The basic idea behind FDM is to replace continuous derivatives with discrete approximations: \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} \\dfrac{\\partial u}{\\partial x} \\approx \\dfrac{u(x+h) - u(x)}{h}, \\quad \\dfrac{\\partial^2 u}{\\partial x^2} \\approx \\dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2} Euler Method The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs. Given the following simple form of ODE \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . Solving ODEs in reverse will be important in future content. Please remember. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Euler Method The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs. Given the following simple form of ODE \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . Solving ODEs in reverse will be important in future content. Please remember. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Euler Method The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs. The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs. Given the following simple form of ODE \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . Given the following simple form of ODE \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . \\dfrac{dy}{dt} = f(t, y(t)) \\dfrac{dy}{dt} = f(t, y(t)) \\dfrac{dy}{dt} = f(t, y(t)) \\dfrac{dy}{dt} = f(t, y(t)) where t\\in[0,1] and notation as y_0=y(0) , y_n=y(t_n) , and y_N=y(1) . t\\in[0,1] t\\in[0,1] y_0=y(0) y_0=y(0) y_n=y(t_n) y_n=y(t_n) y_N=y(1) y_N=y(1) The Euler method can be expressed as y_{n+1} = y_n + hf(t_n, y_n) y_{n+1} = y_n + hf(t_n, y_n) y_{n+1} = y_n + hf(t_n, y_n) y_{n+1} = y_n + hf(t_n, y_n) where h is a step-size, i.e. t_{n+1}-t_n=h=1/N . h h t_{n+1}-t_n=h=1/N t_{n+1}-t_n=h=1/N Now we can solve this ODE starting from the initial point y_0 . By iteratively applying the Euler method, we compute the values of y at discrete points t_n until we reach y_N . y_0 y_0 y y t_n t_n y_N y_N This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . This process involves calculating each subsequent value y_{n+1} using the current value y_n and the function f(t_n, y_n) , progressively moving from t_0 to t_N . y_{n+1} y_{n+1} y_n y_n f(t_n, y_n) f(t_n, y_n) t_0 t_0 t_N t_N Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . Solving ODEs in reverse will be important in future content. Please remember. Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . Solving ODEs in reverse will be important in future content. Please remember. Backward Euler method Considering the following approximation: y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) y_{n+1}-y_{n}=\\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \\approx hf(t_{n+1}, y_{n+1}) We can derive the Euler method in reverse direction: y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1}) This implies that we can solve the ODE in the reverse direction by starting from y_{N} and iteratively applying the formula to compute previous values y_{n} , moving backward from t_{N} to t_{0} . y_{N} y_{N} y_{n} y_{n} t_{N} t_{N} t_{0} t_{0} Solving ODEs in reverse will be important in future content. Please remember. Solving ODEs in reverse will be important in future content. Please remember. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Euler–Maruyama method Those familiar with diffusion models should recognize the following SDE: dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw dx = [f_t(x) - g_t^2 \\nabla_x \\log p_t(x)]dt + g_t dw How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process: x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz x_{t-1} = x_t - \\gamma[f_t(x_t) - g_t^2 \\nabla_{x_t} \\log p_t(x_t)] + \\sqrt{\\gamma}g_tz where \\gamma is a step-size and z \\sim \\mathcal{N}(0,1) . \\gamma \\gamma z \\sim \\mathcal{N}(0,1) z \\sim \\mathcal{N}(0,1) Note that this is how inference in diffusion models works. Note that this is how inference in diffusion models works. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories. Finite Element Method (FEM) FEM is another commonly used method to solve various DEs. t divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method. Example of a mesh grid in FEM: Finite element method - Wikipedia FEM aims to approximate the exact solution u of given PDE as a linear combination of basis (trial) functions \\{\\phi_i\\}_{i=1}^N : u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. What do basis functions look like? These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) The FEM method can be used with the FDM to address more complex and time-dependent PDEs. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. Finite Element Method (FEM) FEM is another commonly used method to solve various DEs. t divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method. Example of a mesh grid in FEM: Finite element method - Wikipedia FEM aims to approximate the exact solution u of given PDE as a linear combination of basis (trial) functions \\{\\phi_i\\}_{i=1}^N : u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. What do basis functions look like? These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) The FEM method can be used with the FDM to address more complex and time-dependent PDEs. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. Finite Element Method (FEM) FEM is another commonly used method to solve various DEs. t divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method. Example of a mesh grid in FEM: Finite element method - Wikipedia FEM is another commonly used method to solve various DEs. t divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method. Example of a mesh grid in FEM: Finite element method - Wikipedia Example of a mesh grid in FEM: Finite element method - Wikipedia Example of a mesh grid in FEM: Finite element method - Wikipedia Finite element method - Wikipedia Finite element method - Wikipedia Finite element method - Wikipedia FEM aims to approximate the exact solution u of given PDE as a linear combination of basis (trial) functions \\{\\phi_i\\}_{i=1}^N : u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. FEM aims to approximate the exact solution u of given PDE as a linear combination of basis (trial) functions \\{\\phi_i\\}_{i=1}^N : u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. u u \\{\\phi_i\\}_{i=1}^N \\{\\phi_i\\}_{i=1}^N u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) u \\approx \\tilde{u}(x) = \\sum_{i=1}^N c_i \\phi_i(x) i.e. Basis functions are used to approximate the solution over each finite element. i.e. Basis functions are used to approximate the solution over each finite element. What do basis functions look like? These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) What do basis functions look like? These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials. 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 1D example: Hat function One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape. 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) By finding the proper linear combination, we can visually verify that \\bar{\\Phi} = \\sum_i N_i can approximate the given \\Phi properly: \\bar{\\Phi} = \\sum_i N_i \\bar{\\Phi} = \\sum_i N_i \\Phi \\Phi partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) 2D example: 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) 2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl) The FEM method can be used with the FDM to address more complex and time-dependent PDEs. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. The FEM method can be used with the FDM to address more complex and time-dependent PDEs. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. Combining both methods allows for efficient management of intricate geometries and dynamic systems. Combining both methods allows for efficient management of intricate geometries and dynamic systems. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena. 💡 Summarizing the content so far, the direction towards better simulation using DL ultimately leads to utilizing DL for improved differential equation solving. 💡 💡 Summarizing the content so far, the direction towards better simulation using DL ultimately leads to utilizing DL for improved differential equation solving. Summarizing the content so far, the direction towards better simulation using DL ultimately leads to utilizing DL for improved differential equation solving. Neural Solver: DL for Solving Differential Equation Neural solvers are DL-based frameworks designed to solve differential equations efficiently and accurately. By training and utilizing existing data from these equations, they aim to predict solutions to complex problems that are computationally intensive for traditional methods. Trend of Accepted Papers Trend of Accepted Papers In recent years, the number of accepted papers on neural solvers has significantly increased. It reflects a growing interest within the scientific community. This trend highlights the potential of DL in transforming traditional problem-solving methods in mathematics and engineering. 2021 2022 2023 2024 NeurIPS 11 17 33 - ICLR 3 6 11 9 ICML 3 3 21 17 2021 2022 2023 2024 2021 2022 2023 2024 2021 2021 2022 2022 2023 2023 2024 2024 NeurIPS 11 17 33 - ICLR 3 6 11 9 ICML 3 3 21 17 NeurIPS 11 17 33 - NeurIPS NeurIPS 11 11 17 17 33 33 - - ICLR 3 6 11 9 ICLR ICLR 3 3 6 6 11 11 9 9 ICML 3 3 21 17 ICML ICML 3 3 3 3 21 21 17 17 The table is based on a survey conducted as of July 10, 2024, so it does not reflect status regarding NeurIPS 2024. The table is based on a survey conducted as of July 10, 2024, so it does not reflect status regarding NeurIPS 2024. Vanilla Approach Why don't we apply DL to the simulation data directly then? Shouldn't it work? Why don't we apply DL to the simulation data directly then? Shouldn't it work? The content available at Supervised training for RANS flows around airfoils — Physics-based Deep Learning provides an excellent example of this and so it will be introduced here. Supervised training for RANS flows around airfoils — Physics-based Deep Learning Training data: Reynolds-Averaged Navier Stokes(RANS) around Airfoils Supervised training for RANS flows around airfoils — Physics-based Deep Learning RANS approach is a fundamental method used in computational fluid dynamics for simulation. Underlying dynamics: Navier-Stokes Equation \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. Training data: Reynolds-Averaged Navier Stokes(RANS) around Airfoils Supervised training for RANS flows around airfoils — Physics-based Deep Learning RANS approach is a fundamental method used in computational fluid dynamics for simulation. Underlying dynamics: Navier-Stokes Equation \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. Reynolds-Averaged Navier Stokes(RANS) around Airfoils Supervised training for RANS flows around airfoils — Physics-based Deep Learning Supervised training for RANS flows around airfoils — Physics-based Deep Learning Supervised training for RANS flows around airfoils — Physics-based Deep Learning RANS approach is a fundamental method used in computational fluid dynamics for simulation. RANS approach is a fundamental method used in computational fluid dynamics for simulation. Underlying dynamics: Navier-Stokes Equation \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. Underlying dynamics: Navier-Stokes Equation \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 \\dfrac{\\partial \\mathbf{u}}{\\partial{t}} + \\mathbf{u} \\cdot \\nabla \\mathbf{u} = - \\dfrac{1}{\\rho} \\nabla p + \\nu \\Delta \\mathbf{u} + \\mathbf{g} \\quad s.t. \\quad \\nabla \\cdot \\mathbf{u} = 0 where \\mathbf{u} is flow velocity, p is pressure, \\rho is mass density, \\nu is diffusion constant for viscosity, and \\mathbf{g} is body acceleration (e.g. gravity, inertia acceleration). \\mathbf{u} \\mathbf{u} p p \\rho \\rho \\nu \\nu \\mathbf{g} \\mathbf{g} The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems. DL model: Simple U-net shape architecture with naive supervised loss Supervised training for RANS flows around airfoils — Physics-based Deep Learning \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 where x_i are the boundary conditions shown on the left side of the figure, and the target y_i are the pre-computed field quantities like pressure and velocity on the right side. DL model: Simple U-net shape architecture with naive supervised loss Supervised training for RANS flows around airfoils — Physics-based Deep Learning \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 where x_i are the boundary conditions shown on the left side of the figure, and the target y_i are the pre-computed field quantities like pressure and velocity on the right side. Supervised training for RANS flows around airfoils — Physics-based Deep Learning Supervised training for RANS flows around airfoils — Physics-based Deep Learning Supervised training for RANS flows around airfoils — Physics-based Deep Learning \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 \\text{arg min}_{\\theta} \\sum_i ( f_\\theta(x_i)-y_i )^2 where x_i are the boundary conditions shown on the left side of the figure, and the target y_i are the pre-computed field quantities like pressure and velocity on the right side. x_i x_i y_i y_i Results: Not Optimal Supervised training for RANS flows around airfoils — Physics-based Deep Learning The vanilla DL approach shows limitations in capturing the complex dynamics of around airfoils, with predictions often not aligning closely with physical data, suggesting a need for more advanced models or training methods. Results: Not Optimal Supervised training for RANS flows around airfoils — Physics-based Deep Learning The vanilla DL approach shows limitations in capturing the complex dynamics of around airfoils, with predictions often not aligning closely with physical data, suggesting a need for more advanced models or training methods. Supervised training for RANS flows around airfoils — Physics-based Deep Learning Supervised training for RANS flows around airfoils — Physics-based Deep Learning Supervised training for RANS flows around airfoils — Physics-based Deep Learning The vanilla DL approach shows limitations in capturing the complex dynamics of around airfoils, with predictions often not aligning closely with physical data, suggesting a need for more advanced models or training methods. Next Steps in Advancing Neural Solvers In the upcoming posts, we will explore innovative neural solver methodologies that promise greater fidelity and robustness to address the challenges shown in vanilla DL approach. Specifically, three advanced methods will be introduced: Neural Ordinary Differential Equations (NeuralODE) , Physics-Informed Neural Networks (PINN) , and Neural Operators . Neural Ordinary Differential Equations (NeuralODE) Physics-Informed Neural Networks (PINN) Neural Operators NeuralODE represents a fusion of deep learning with differential equations, offering a continuous-time model that can dynamically adapt to the underlying physics. NeuralODE represents a fusion of deep learning with differential equations, offering a continuous-time model that can dynamically adapt to the underlying physics. NeuralODE PINN incorporates physical laws into the neural network’s loss function, enhancing prediction accuracy and model generalizability. PINN incorporates physical laws into the neural network’s loss function, enhancing prediction accuracy and model generalizability. PINN Neural Operators learn mappings in infinite-dimensional spaces, enabling broad applicability across different geometries and conditions. Neural Operators learn mappings in infinite-dimensional spaces, enabling broad applicability across different geometries and conditions. Neural Operators These methods not only address traditional DL model limitations but also offer new ways to simulate complex physical systems with high precision. The upcoming post will delve into a detailed examination of each technique, their potential, and their applications."}, {"id": "LLM4Finance", "title": "LLM4Finance", "path": "pages/projects/LLM4Finance/content.html", "date": "Since May 2023", "tags": ["LLM", "AI4Finance", "MLOps"], "content": "LLM4Finance May 2023 - Current May 2023 - Current Developing an LLM-powered finance dashboard to optimize personal investment strategies, integrating financial engineering techniques. Pipeline Outline Pipeline Outline Pipeline Outline MLOps Pipeline for Sentiment Analysis PEFT is applied to LLM to predict the sentiment of crawled news headlines: positive, neutral, or negative. Continuous training is triggered in a HuggingFace space environment when the amount of newly labeled data surpasses a specified threshold. Continuous deployment then follows in a separate HuggingFace space environment. The trained model is quantized with llama.cpp framework for deployment in a free basic CPU space of HF. The HuggingFace model hub is used to save and load trained models. MLOps Pipeline for Sentiment Analysis MLOps Pipeline for Sentiment Analysis PEFT is applied to LLM to predict the sentiment of crawled news headlines: positive, neutral, or negative. Continuous training is triggered in a HuggingFace space environment when the amount of newly labeled data surpasses a specified threshold. Continuous deployment then follows in a separate HuggingFace space environment. The trained model is quantized with llama.cpp framework for deployment in a free basic CPU space of HF. The HuggingFace model hub is used to save and load trained models. PEFT is applied to LLM to predict the sentiment of crawled news headlines: positive, neutral, or negative. Continuous training is triggered in a HuggingFace space environment when the amount of newly labeled data surpasses a specified threshold. Continuous training is triggered in a HuggingFace space environment when the amount of newly labeled data surpasses a specified threshold. Continuous deployment then follows in a separate HuggingFace space environment. The trained model is quantized with llama.cpp framework for deployment in a free basic CPU space of HF. The trained model is quantized with llama.cpp framework for deployment in a free basic CPU space of HF. The HuggingFace model hub is used to save and load trained models. LLM Application LLM agents are crafted for data processing processes data: auto-labels sentiment data and controls the quality of crawled news headlines. Error analysis revealed that data quality issues, such as punctuation and duplication, can severely impact model performance. LLM agents were crafted to address this. The LLM assistant is designed to enhance and streamline financial statement analysis, providing comprehensive support and insights. Experimental: RAG-based framework for enhanced detailed analysis. Experimental: Multiple LLM agents are crafted to simulate economic investment environment to respond to specific scenarios, each tailored to different investing personas. LLM Application LLM Application LLM agents are crafted for data processing processes data: auto-labels sentiment data and controls the quality of crawled news headlines. Error analysis revealed that data quality issues, such as punctuation and duplication, can severely impact model performance. LLM agents were crafted to address this. The LLM assistant is designed to enhance and streamline financial statement analysis, providing comprehensive support and insights. Experimental: RAG-based framework for enhanced detailed analysis. Experimental: Multiple LLM agents are crafted to simulate economic investment environment to respond to specific scenarios, each tailored to different investing personas. LLM agents are crafted for data processing processes data: auto-labels sentiment data and controls the quality of crawled news headlines. Error analysis revealed that data quality issues, such as punctuation and duplication, can severely impact model performance. LLM agents were crafted to address this. Error analysis revealed that data quality issues, such as punctuation and duplication, can severely impact model performance. LLM agents were crafted to address this. The LLM assistant is designed to enhance and streamline financial statement analysis, providing comprehensive support and insights. Experimental: RAG-based framework for enhanced detailed analysis. Experimental: RAG-based framework for enhanced detailed analysis. Experimental: Multiple LLM agents are crafted to simulate economic investment environment to respond to specific scenarios, each tailored to different investing personas."}]