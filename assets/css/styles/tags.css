/* Tags Layout */
.tags-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* Tag Design */
.tag-board {
  display: flex;
  align-items: center;
  background: #3366cc;
  border-radius: 4px;
  overflow: hidden;
  color: white;
  font-size: 0.75em;
  height: auto;
  box-sizing: border-box;
}

body.dark-mode .tag-board {
  background: #e68a4d;
}

.tag-name {
  color: white;
  padding: 4px 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  height: auto;
  box-sizing: border-box;
  white-space: nowrap;
}

.tag-count {
  background: #e8e8e8;
  color: #333;
  padding: 4px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  height: auto;
  box-sizing: border-box;
  white-space: nowrap;
}

body.dark-mode .tag-count {
  background: #aeaeae;
  color: #ffffff;
}

/* See All Button */
.see-all {
  display: inline-block;
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
  background: #3366cc;
  color: #ffffff;
  font-size: 0.75em;
  text-decoration: none;
  padding: 2px 8px;
  border-radius: 4px;
}

/* Disable hover effects */
.see-all:hover,
.see-all:focus {
  background: #3366cc; /* Maintain the original background */
  color: #ffffff; /* Maintain the original text color */
  text-decoration: none; /* Ensure no underline or hover styles */
}

body.dark-mode .see-all {
  background: #e68a4d;
  color: #ffffff;
}

body.dark-mode .see-all:hover,
body.dark-mode .see-all:focus {
  background: #e68a4d;
  color: #ffffff;
}
