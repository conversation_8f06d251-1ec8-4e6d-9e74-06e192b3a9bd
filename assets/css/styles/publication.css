/* Publication Style */

.pubs-section .pub-card {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
  transition: 0.3s;
  border: 1px solid rgb(90, 90, 90);
  color: #404040; /* Light mode text color */
}

.pubs-section .pub-card:hover {
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}

.pubs-section .pub-card .pub-card-teaser {
  margin: auto;
  margin-bottom: 6px;
  display: block;
  width: 95%;
  height: 100px;
}

.pubs-section .pub-card .pub-card-body .pub-title {
  color: #404040; /* Light mode title color */
  margin-top: 10px;
  margin-bottom: 0;
  font-weight: 500;
}

/* Author Styling */
.pubs-section .pub-card .pub-card-body .pub-author {
  margin-top: 5px;
  margin-bottom: 5px;
  font-size: 15px;
  color: #4f5966; /* Light mode author color */
}
/* Source Styling (Default Mode) */
.pubs-section .pub-card .pub-card-body .pub-src {
  margin-top: 5px;
  margin-bottom: 5px;
  font-size: 15px;
}

.pubs-section .pub-card .pub-card-body .pub-src a {
  color: #0044cc; /* Thick blue color */
}

.pubs-section .pub-card .pub-card-body .pub-src a:hover,
.pubs-section .pub-card .pub-card-body .pub-src a:focus {
  text-decoration: underline;
}

/* Dark Mode */
body.dark-mode .pubs-section .pub-card {
  color: #cfcfcf; /* Dark mode text color */
  border: 1px solid #555; /* Dark mode border color */
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
}

body.dark-mode .pubs-section .pub-card:hover {
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.4);
}

body.dark-mode .pubs-section .pub-card .pub-card-body .pub-title {
  color: #ffffff; /* Dark mode title color */
}

body.dark-mode .pubs-section .pub-card .pub-card-body .pub-author {
  color: #b0b0b0; /* Dark mode author color */
}

body.dark-mode .pubs-section .pub-card .pub-card-body .pub-src a {
  color: #ffcc00; /* Thick yellow color */
}
