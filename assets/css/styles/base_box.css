/* Base box for all contents */
.content-box {
  background-color: #ffffff; /* Light mode background */
  border-radius: 10px; /* Rounded corners for the box */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Light mode shadow */
  padding: 1.5rem; /* Padding inside the box */
  margin-top: 0.5rem;
  margin-bottom: 0.5rem; /* Spacing between boxes */
  transition: all 0.3s ease; /* Smooth transition for hover effect */
}

/* Box hover effect for better interaction */
.content-box:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

/* Container for the main content */
.container-content {
  max-width: 1200px; /* Limiting the maximum width */
  margin: 0 auto; /* Center the container */
  padding: 2rem; /* Padding around the container */
}

/* Specific styles for the footer and other sections within the box */
footer {
  background-color: #ffffff; /* Light mode footer background */
  padding: 1rem 0; /* Footer padding */
  margin-top: 1.5rem;
  border-radius: 8px; /* Rounded corners for the footer */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Light mode shadow */
}

footer p.text-muted {
  color: #777; /* Muted text color */
  font-size: 0.875rem;
}

footer a {
  color: #606060; /* Light mode link color */
}

footer a:hover,
footer a:focus {
  color: #0085a1; /* Hover color for links */
}

/* Styling for the content inside each box like images, paragraphs, etc */
.content-box img {
  max-width: 100%; /* Ensure images are responsive */
  border-radius: 5px; /* Rounded corners for images */
}

.content-box h1,
.content-box h2,
.content-box h3 {
  color: #333; /* Dark text color for headings */
  margin-bottom: 1rem; /* Space between the heading and content */
}

.content-box p {
  color: #555; /* Slightly darker text color for paragraphs */
  line-height: 1.6; /* Line spacing for readability */
}

/* --- Dark Mode --- */
body.dark-mode .content-box {
  background-color: #2f2f2f; /* Dark mode background */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5); /* Dark mode shadow */
}

body.dark-mode .content-box:hover {
  background-color: #373737; /* Dark mode background */
}

body.dark-mode .content-box h1,
body.dark-mode .content-box h2,
body.dark-mode .content-box h3 {
  color: #ffffff; /* Dark mode text color for headings */
}

body.dark-mode .content-box p {
  color: #cfcfcf; /* Slightly lighter text color for paragraphs in dark mode */
}

body.dark-mode footer {
  background-color: #2f2f2f; /* Dark mode footer background */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5); /* Dark mode footer shadow */
}

body.dark-mode footer p.text-muted {
  color: #bfbfbf; /* Dark mode muted text */
}

body.dark-mode footer a {
  color: #cfcfcf; /* Dark mode link color */
}

body.dark-mode footer a:hover,
body.dark-mode footer a:focus {
  color: #ffcc00; /* Hover color for links in dark mode */
}
