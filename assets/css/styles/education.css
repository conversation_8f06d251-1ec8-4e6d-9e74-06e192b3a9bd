/* Timeline Container for Education Section */

.timeline {
  margin: 20px auto;
}

/* Outer Layer with the timeline border */
.outer {
  border-left: 3px solid #333;
}

body.dark-mode .outer {
  border-left: 3px solid #888; /* Lighter border in dark mode for better visibility */
}

/* Card container */
.card {
  position: relative;
  margin: 0 0 20px 20px;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  background: #ececec; /* Light mode background */
  color: #606060; /* Light mode text */
  border-radius: 8px;
  max-width: 450px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}

body.dark-mode .card {
  background: inherit; /* Inherit the background color from the parent */
  color: #cfcfcf; /* Lighter text color */
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.5); /* Stronger shadow for dark mode */
}

/* Layout of the timeline */
.card .info_column {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.card .info_row {
  display: flex;
  flex-direction: row;
}

/* Title of the card */
.card .title {
  color: #404040; /* Light mode title */
  font-size: 20px;
  position: relative;
}

body.dark-mode .card .title {
  color: #ffffff; /* White title text for dark mode */
}

/* Timeline dot  */
.card .title::before {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background: white; /* Light mode dot */
  border-radius: 999px;
  left: -37px;
  border: 3px solid rgb(90, 90, 90);
}

body.dark-mode .card .title::before {
  background: #444; /* Gray dot for dark mode */
  border: 3px solid #888; /* Lighter border for dark mode */
}

/* School Logo */
.logoSNU {
  background: url(/assets/img/profile/snu.png) no-repeat center center;
  width: 80px;
  opacity: 85%;
  margin-right: 5px;
  background-size: contain;
}

body.dark-mode .logoSNU {
  opacity: 100%; /* Make the logo more visible in dark mode */
}
