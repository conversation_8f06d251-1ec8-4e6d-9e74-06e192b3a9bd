/* Search Icon */

/* Remove focus outline for the search button */
#search-toggle {
  outline: none; /* Removes the focus outline */
  border: none; /* Removes any border applied */
  background: none; /* Ensures no default background */
}

/* Optional: Add subtle focus styles for better UX */
#search-toggle:focus {
  outline: none;
  box-shadow: none; /* Remove shadow effect */
  background-color: transparent; /* Keep background consistent */
}

.search-toggle:hover {
  color: #0085a1; /* Change color on hover */
}

body.dark-mode .search-toggle:hover {
  color: #ffcc00; /* Change color on hover */
}

/* Full-Screen Overlay */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.65);
  display: none; /* Hidden by default */
  z-index: 9999;
  justify-content: center;
  align-items: flex-start; /* Align items to the top */
  padding-top: 100px; /* Add spacing from the top for the search box */
}

.search-overlay.active {
  display: flex;
}

/* Search Box */
.search-box {
  background-color: #ffffff; /* Dark background */
  width: 80%; /* Take a good percentage of the screen width */
  border-radius: 4px; /* Rounded input */
  max-width: 700px; /* Limit max width */
  color: #404040; /* Text color */
  position: relative; /* For positioning the close button */
  margin: 0 auto; /* Center align the box */
}

body.dark-mode .search-box {
  background-color: #2f2f2f; /* Dark background */
  color: #ffffff; /* Light text color */
}

/* Search Input */
.search-input {
  width: 100%; /* Full width of the box */
  padding: 10px; /* Comfortable padding */
  font-size: 1rem; /* Standardized font size */
  border: 0px; /* Remove default border */
  border-radius: 4px; /* Rounded input */
  border-bottom-left-radius: 0; /* Remove border radius on the left */
  border-bottom-right-radius: 0;
  border-bottom: 1px solid #404040;
  margin: 0; /* Align flush with the box */
}

body.dark-mode .search-input {
  border-bottom: 1px solid #ffcc00;
  background-color: #2f2f2f; /* Dark background */
  color: #ffffff; /* Light text color */
}

.search-input:focus {
  outline: none; /* Remove default focus outline */
}

/* Close Button */
.close-search {
  outline: none; /* Removes the focus outline */
  border: none; /* Removes any border applied */
  position: absolute;
  top: 7px; /* Align to the top */
  right: 10px; /* Align to the right */
  background: none;
  border: none;
  color: #404040; /* Neutral color for the close icon */
  font-size: 1.2rem; /* Make the icon slightly larger */
}

/* Optional: Add subtle focus styles for better UX */
.close-search:focus {
  outline: none;
  box-shadow: none; /* Remove shadow effect */
  background-color: transparent; /* Keep background consistent */
}

.close-search:hover {
  color: #0085a1; /* Change color on hover */
}

body.dark-mode .close-search {
  color: #ffffff; /* Light text color */
}

body.dark-mode .close-search:hover {
  color: #ffcc00; /* Change color on hover */
}

/* Search Placeholder */
.search-placeholder {
  text-align: center;
  color: #404040;
  margin-top: 10px;
  font-family: 'Arial', sans-serif;
}

.search-placeholder-icon {
  font-size: 30px;
  margin-bottom: 10px;
}

.search-placeholder-text {
  font-size: 15px;
  font-weight: bold;
}

body.dark-mode .search-placeholder {
  color: #ffffff;
}

/* Search Results Container */
.search-results-container {
  max-height: 400px; /* Limit height with scrolling for large results */
  overflow-y: auto; /* Enable vertical scrolling */
  padding: 10px; /* Internal spacing for content */
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 10px; /* Add spacing from the top */
  margin-bottom: 10px; /* Add spacing from the bottom */
}

#search-data-display {
  font-family: monospace; /* Monospace font for JSON data */
  font-size: 14px; /* Readable font size */
  white-space: pre-wrap; /* Preserve formatting and wrap long lines */
  color: #333; /* Dark text color */
  overflow-x: auto; /* Allow horizontal scrolling if needed */
  padding-bottom: 0px;
}

body.dark-mode #search-data-display {
  color: #ffffff; /* Light text color */
}

/* Search Result Box */
.search-result-box {
  display: block;
  border: 1px solid #404040; /* Optional: Add a border for clear separation */
  border-radius: 6px; /* Optional: Add rounded corners for a modern look */
  background-color: inherit; /* Inherit the background color */
  margin-top: 10px;
}

.search-result-box:hover {
  background-color: #f5f5f5; /* Light gray hover effect */
}

body.dark-mode .search-result-box {
  border: 1px solid #ffffff; /* Yellow border in dark mode */
}

body.dark-mode .search-result-box:hover {
  background-color: #373737; /* Slightly lighter hover effect */
}

.search-item-title {
  margin-top: -30px;
  margin-bottom: -30px;
  margin-left: 10px;
  margin-right: 10px;
  font-size: 1.0em !important;
  font-weight: bold;
  font-family: 'Arial', sans-serif;
  color: #404040;
}

body.dark-mode .search-item-title {
  color: #ffffff;
}

.search-item-date,
.search-item-tags {
  margin-left: 10px;
  margin-right: 10px;
  font-size: 0.8rem !important;
  font-family: 'Arial', sans-serif;
  color: #005f73 !important;
}

.search-item-tags {
  margin-top: -30px;
  margin-bottom: -30px;
}

body.dark-mode .search-item-date,
body.dark-mode .search-item-tags {
    color: #f4a261 !important;
}

/* Style for 'No results found' message */
.no-results, .search-error {
  text-align: center;
  color: #404040;
  font-family: 'Arial', sans-serif;
  font-size: 15px; /* Small font size for compact display */
  font-weight: bold;
  margin-top: 15px;
}

.search-error {
  color: #d9534f; /* Red for errors */
}

body.dark-mode .no-results {
  color: #ffffff;
}

body.dark-mode .search-error {
  color: #ffcc00; /* Yellow for errors in dark mode */
}