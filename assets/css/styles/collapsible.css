/* For collapsible design */

details > summary {
  display: flex;
  align-items: center;
  list-style: none;
}

details > summary::-webkit-details-marker {
  display: none;
  /* Remove default marker on Safari and other webkit browsers. This is necessary for safari case. */
}

summary {
  display: block;
  padding: 1.5rem 3rem 1rem 0;
  font-weight: 600;
  cursor: pointer;
  position: relative;
}

summary::before,
summary::after {
  content: "";
  position: absolute;
  width: 1rem;
  height: 2px;
  background-color: #222;
  right: 1rem;
  top: 2.25rem;
}

/* Dark Mode */
body.dark-mode summary::before,
body.dark-mode summary::after {
  background-color: #fff; /* Lighter color for dark mode */
}

summary::after {
  transform: rotateZ(90deg);
}

details[open] > summary::after {
  display: none;
}

details[open] > summary {
  padding-bottom: 0; /* Reduces the bottom padding when details is open */
}

/* Additional Styles */
.secondary-info {
  color: grey; /* Grey color for the text */
  font-style: italic; /* Italicized font */
  font-size: 15px;
}

.display_column {
  display: flex;
  flex-direction: column;
}

.summary-container {
  display: flex;
  margin-left: -15px;
}

.custom-ul {
  padding-left: 10px;
  margin: 15px;
  font-size: 0.85em;
}
