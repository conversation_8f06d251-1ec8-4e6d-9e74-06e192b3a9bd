/* Reading Progress Bar */
.reading-progress-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.reading-progress-container.visible {
  opacity: 1;
  visibility: visible;
}

.reading-progress-container.visible .reading-progress-bar {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.reading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #0085a1, #00a8cc);
  width: 0%;
  transition: width 0.1s ease-out;
  border-radius: 0 2px 2px 0;
}

/* Dark mode styles */
body.dark-mode .reading-progress-container {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .reading-progress-bar {
  background: linear-gradient(90deg, #ffcc00, #ffd700);
}

/* Ensure the progress bar doesn't interfere with navbar and other fixed elements */
.navbar {
  position: relative;
  z-index: 1030;
}

/* Ensure scroll-to-top button stays above progress bar */
#scroll-to-top {
  z-index: 10000;
}
