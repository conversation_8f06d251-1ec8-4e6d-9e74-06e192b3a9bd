/* Figure and caption styling */
figure {
  margin: 1.25em 0;
  page-break-inside: avoid;
}

figcaption {
  opacity: 0.5;
  font-size: 85%;
  margin-top: 0.5em;
}

body.dark-mode figcaption {
  opacity: 0.7; /* Slightly more visible in dark mode */
  color: #b0b0b0;
}

/* Image */
.image {
  border: none;
  margin: 1.5em 0;
  padding: 0;
  border-radius: 0;
  text-align: center;
}

/* Table of content styling */
.table_of_contents {
  background-color: #f5f5f5;
  border-radius: 5px;
  padding-left: 20px;
  padding-bottom: 10px;
  font-family: Arial, sans-serif;
}

body.dark-mode .table_of_contents {
  background-color: #2a2a2a; /* Dark mode background */
  color: #eaeaea; /* Dark mode text */
}

.table_of_contents-item {
  display: block;
  font-size: 0.95rem;
  line-height: 1.3;
  padding: 0.125rem;
}

.table_of_contents-indent-1 {
  margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
  margin-left: 3rem;
}

.table_of_contents-indent-3 {
  margin-left: 4.5rem;
}

.table_of_contents-link {
  text-decoration: none !important;
  opacity: 0.7;
  border-bottom: 1px solid rgba(55, 53, 47, 0.18);
  color: inherit; /* Inherit color from parent */
}

.table_of_contents-link:hover {
  opacity: 1;
  color: inherit; /* Keep the color same on hover */
}

body.dark-mode .table_of_contents-link {
  border-bottom: 1px solid rgba(255, 255, 255, 0.18); /* Adjust border for dark mode */
  color: #b0b0b0; /* Dark mode link color */
}

body.dark-mode .table_of_contents-link:hover {
  opacity: 1;
  color: #ffcc00; /* Highlight hover color for dark mode */
}

/* For callout block */
.notion_callout {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  font-family: Arial, sans-serif;
  color: #333;
  max-width: 800px;
}

.notion_callout .icon {
  font-size: 24px;
  margin-left: 15px;
  margin-right: 10px;
}

.notion_callout .text {
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 20px;
}

body.dark-mode .notion_callout {
  background-color: #2a2a2a; /* Dark mode background */
  color: #eaeaea; /* Dark mode text */
}

body.dark-mode .notion_callout .icon {
  color: #ffcc00; /* Highlight for icons in dark mode */
}

/* blockquote */
blockquote {
  border-left: 4px solid #2e2e2e;
  padding-left: 1em;
  margin-top: 20px;
  margin-left: 0;
  color: inherit !important;
  font-style: normal;
}

body.dark-mode blockquote {
  border-left: 4px solid #ffcc00; /* Highlighted border in dark mode */
}

/* Table */
.simple-table {
  margin-top: 0px;
  margin-bottom: 20px;
  font-size: 0.875rem;
  empty-cells: show;
  border-collapse: collapse;
  overflow-x: auto;
  max-width: 100%;
  margin-left: auto; /* Center align the table */
  margin-right: auto; /* Center align the table */
}

.simple-table th,
.simple-table td {
  height: 29px;
  min-width: 60px;
  white-space: nowrap; /* Prevents text wrapping */
}

.simple-table-header-color {
  background: rgb(247, 246, 243);
  color: black;
}

body.dark-mode .simple-table-header-color {
  background: #444; /* Dark mode table header background */
  color: #fff; /* Light text for dark mode */
}

.simple-table-header {
  font-weight: 500;
}

@media only screen and (max-width: 600px) {
  .simple-table th,
  .simple-table td {
    padding: 5px;
    white-space: normal; /* Allow text wrapping if necessary on small screens */
  }
}

/* Code */
code {
  margin-right: -5px;
  display: inline;
  white-space: normal;
}

/* Equations */
.equation-container {
  width: 100%;
  overflow-x: auto; /* allows horizontal scrolling */
  white-space: nowrap; /* prevents equation break across lines */
}

.katex-display {
  overflow: visible;
}

.katex-display > .katex > .katex-html > .tag {
  position: absolute;
  padding-left: 2rem;
}

.tag-overflowed {
  display: inline-block;
  position: relative !important;
}

.notion-text-equation-token {
  overflow-x: auto; /* allows horizontal scrolling */
  white-space: nowrap; /* prevents equation break across lines */
  padding-left: 1px;
  padding-right: -2px;
}

.equation_math {
  white-space: wrap;
}

/* Dots */
.bulleted-list {
  margin-left: -20px;
  color: inherit !important;
}

.notion-li {
  margin-top: 10px;
  color: inherit !important;
}

.notion-paragraph {
  margin-top: 10px;
  margin-bottom: 10px;
  color: inherit !important;
}

.numbered-list {
  margin-left: -20px;
  color: inherit !important;
}
