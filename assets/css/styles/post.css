/* Post title */
.post-title {
  margin-top: 5px;
  margin-bottom: 10px; /* Space between title and date */
}

.post-title a {
  color: inherit; /* Use content-box text color */
}

.post-title a:hover {
  color: #0085a1; /* Highlight hover color */
  text-decoration: none;
}

/* Post date */
.post-date {
  font-size: 0.9rem; /* Smaller text for dates */
  font-weight: 400;
  color: #606060; /* Muted text color */
  margin-top: 5px; /* Add spacing above date */
  margin-bottom: 5px;
}

/* Tags and Button Container */
.tags-and-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  flex-wrap: wrap; /* Allow items to wrap on narrow screens */
}

/* Tags */
.post-tags {
  margin: 0;
  margin-right: 5px;
  font-size: 0.9rem;
  font-style: italic;
  flex: 1; /* Allow tags to take up available space */
}

.post-tags i {
  margin-right: 10px; /* Space between icon and text */
  font-size: 0.9rem;
}

/* Read More Button */
.read-more-btn {
  display: inline-flex; /* Align icon and text */
  align-items: center; /* Center align icon and text */
  justify-content: center; /* Center text horizontally */
  background-color: #f9f9f9; /* Button background color */
  color: #606060; /* Button text color */
  text-decoration: none; /* Remove underline */
  padding: 6px 12px; /* Reduced padding for a smaller button */
  border-radius: 6px; /* Adjusted rounded corners */
  font-size: 0.8rem; /* Slightly smaller font size */
  font-weight: 500; /* Slightly bold text */
  border: 1px solid #dcdcdc; /* Border */
  transition: background-color 0.3s ease, color 0.3s ease; /* Smooth color transition */
  margin-left: auto; /* Push button to the right */
  position: relative;
}

.read-more-btn i {
  margin-right: 6px; /* Reduced space between icon and text */
  font-size: 0.9rem; /* Slightly smaller icon size */
}

.read-more-btn:hover {
  background-color: #eeeeee; /* Slightly darker background on hover */
  color: #404040; /* Darker text color */
}

/* Dark mode styles */
body.dark-mode .post-title a {
  color: #ffffff; /* Light link color for dark mode */
}

body.dark-mode .post-title a:hover {
  color: #ffcc00; /* Highlight hover color for links in dark mode */
}

body.dark-mode .post-date {
  color: #aaa; /* Muted text color for date in dark mode */
}

body.dark-mode .post-tags {
  color: #aaa !important; /* Muted text color for tags in dark mode */
}

body.dark-mode .read-more-btn {
  background-color: #333; /* Dark mode button background */
  color: #eaeaea; /* Button text color for dark mode */
  border: 1px solid #444; /* Border for dark mode */
}

body.dark-mode .read-more-btn:hover {
  background-color: #444; /* Highlight hover background for dark mode */
  color: #ffcc00; /* Highlight hover text color for dark mode */
}
