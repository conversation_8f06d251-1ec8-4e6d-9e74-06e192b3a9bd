/* --- Footer --- */

footer {
  padding: 1.25rem 0;
  margin-top: 2.5rem;
  font-size: 0.875rem;
  background-color: #ffffff; /* Light mode background */
  border-top: 0.5px solid #DDDDDD; /* Light mode border */
}

footer p.text-muted {
  color: #404040 !important;
  margin-bottom: 0;
  font-size: 0.875rem; /* Smaller font size for copyright text */
}

footer a {
  color: #606060; /* Link color */
}

footer a:hover,
footer a:focus {
  color: #0085A1; /* Hover color */
}

footer .social-icons {
  font-size: 1rem; /* Smaller icon size */
}

footer .social-icons i {
  margin-right: 0.5rem; /* Add spacing between social media icons */
}

/* Align social icons to the right and copyright to the left */
footer .social-icons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

footer .copyright {
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  text-align: center;
  margin-bottom: 0;
  margin-top: 0;
}

/* Dark Mode Styles */
body.dark-mode footer {
  background-color: #2a2a2a; /* Dark mode background */
  border-top: 1px solid #444444; /* Dark mode border */
}

body.dark-mode footer p.text-muted {
  color: #ffffff !important; /* Lighter text color for dark mode */
}

body.dark-mode footer a {
  color: #b0b0b0; /* Lighter link color for dark mode */
}

body.dark-mode footer a:hover,
body.dark-mode footer a:focus {
  color: #ffcc00; /* Dark mode hover color */
}

body.dark-mode footer .social-icons i {
  color: #ffffff; /* White icons in dark mode */
}

body.dark-mode footer .social-icons i:hover {
  color: #ffcc00; /* Hover color for icons in dark mode */
}

/* Adjust footer content on larger screens */
@media (min-width: 768px) {
  footer {
    padding: 1.5rem 0;
  }

  footer .footer-custom-content {
    font-size: 1rem;
  }
}
