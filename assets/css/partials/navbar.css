---
layout: null
---

/* --- Navbar --- */

.navbar-custom {
    background-color: #ffffff; /* Light mode background */
    border-bottom: 1px solid #DDDDDD; /* Light mode border */
    font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Light mode shadow */
    -webkit-transition: padding .5s ease-in-out, background-color .3s ease, color .3s ease, box-shadow .3s ease;
    -moz-transition: padding .5s ease-in-out, background-color .3s ease, color .3s ease, box-shadow .3s ease;
    transition: padding .5s ease-in-out, background-color .3s ease, color .3s ease, box-shadow .3s ease;
}

.navbar-custom,
.navbar-custom.top-nav-short,
.navbar-custom.top-nav-short-permanent {
    padding-top: 0;
    padding-bottom: 0;
}

.navbar-custom .navbar-brand {
    line-height: 1.5;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    font-size: 1.125rem;
    color: #404040; /* Light mode text color */
}

.navbar-custom .navbar-brand-logo img {
    height: 2.5rem;
    -webkit-transition: height .5s ease-in-out;
    -moz-transition: height .5s ease-in-out;
    transition: height .5s ease-in-out;
}

.navbar-custom .navbar-nav .nav-item {
    text-transform: uppercase;
    font-size: 0.8125rem;
    letter-spacing: 0.0625rem;
}

.navbar-custom .navbar-nav .nav-link {
    line-height: 1.25rem;
    padding-top: 0.9375rem;
    padding-bottom: 0.9375rem;
}

.navbar-custom .navbar-brand,
.navbar-custom .navbar-nav .nav-link {
    font-weight: 700;
    font-size: 1.1em;
    color: #404040; /* Light mode text color */
}

.navbar-custom .navbar-brand{
  font-size: 1.0em
}

.navbar-toggler {
  font-size: 1rem;
  margin: 0.5rem 0;
  border: 1px solid #DDDDDD; /* Default light gray border */
  background-color: transparent; /* Ensure no background */
  outline: none; /* Prevent focus outline */
  box-shadow: none; /* Prevent focus/active box shadow */
}

.navbar-custom .navbar-toggler:focus,
.navbar-custom .navbar-toggler:hover {
  background-color: transparent; /* Prevent hover background color */
  border: 1px solid #DDDDDD; /* Maintain the default light gray border */
  outline: none; /* Prevent focus outline */
  box-shadow: none !important; /* Prevent focus box shadow */
}

.navbar-custom .navbar-toggler:active,
.navbar-custom .navbar-toggler[aria-expanded="true"] {
  background-color: transparent !important; /* Prevent background change on expand */
  border: 1px solid #DDDDDD !important; /* Maintain default border */
  outline: none !important; /* Prevent outline */
  box-shadow: none !important; /* Prevent box shadow */
}


/* Dropdown Toggle */
.dropdown-toggle::after {
    border-width: 0.4em;
}

@media (min-width: 1000px) {
    .navbar-custom {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .navbar-custom .navbar-brand-logo img {
        height: 3.125rem;
    }

    .navbar-expand-xl .navbar-nav .nav-link {
        padding-left: 0.9375rem;
        padding-right: 0.9375rem;
    }

    .navbar-expand-xl .navbar-nav .nav-item:not(.dropdown):last-child .nav-link {
        padding-right: 0;
    }
}

.navbar-custom .nav-item.dropdown .dropdown-menu {
    background-color: #ffffff; /* Light mode dropdown background */
    color: #404040; /* Light mode dropdown text */
    border: 1px solid #DDDDDD; /* Light mode dropdown border */
}

.navbar-custom .nav-item.dropdown .dropdown-menu .dropdown-item {
    padding: 0.625rem;
    background-color: #ffffff; /* Light mode background */
    color: #404040; /* Light mode text */
}

.navbar-custom .navbar-brand:hover,
.navbar-custom .navbar-brand:focus,
.navbar-custom .navbar-nav .nav-link:hover,
.navbar-custom .navbar-nav .nav-link:focus,
.navbar-custom .nav-item.dropdown .dropdown-menu .dropdown-item:hover {
    color: #0085A1; /* Light mode hover color */
}

/* Light Mode Button */
.navbar-custom .theme-toggle i {
  color: #404040; /* Default dark color for light mode */
  transition: color 0.3s ease; /* Smooth transition */
}

.navbar-custom .theme-toggle i:hover {
  color: #0085A1; /* Highlight hover color for light mode */
}

/* --- Dark Mode --- */
body.dark-mode .navbar-custom {
  background-color: #2a2a2a; /* Dark mode background */
  border-bottom: 1px solid #444444; /* Dark mode border */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); /* Dark mode shadow */
}

body.dark-mode .navbar-custom .navbar-brand,
body.dark-mode .navbar-custom .navbar-nav .nav-link {
  color: #ffffff; /* Dark mode text color */
}

body.dark-mode .navbar-custom .navbar-brand:hover,
body.dark-mode .navbar-custom .navbar-nav .nav-link:hover {
  color: #ffcc00; /* Dark mode hover color */
}

body.dark-mode .navbar-toggler {
  border: 1px solid #ffffff; /* White border for dark mode button */
  background-color: transparent; /* Ensure no background */
  outline: none; /* Prevent focus outline */
  box-shadow: none; /* Prevent focus/active box shadow */
}

body.dark-mode .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3E%3Cpath stroke='rgba(255,255,255,1)' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
  /* White hamburger icon */
}

body.dark-mode .navbar-custom .navbar-toggler:focus,
body.dark-mode .navbar-custom .navbar-toggler:hover {
  background-color: transparent; /* Prevent hover background color */
  border: 1px solid #ffffff; /* Maintain white border */
}

body.dark-mode .navbar-custom .navbar-toggler:active,
body.dark-mode .navbar-custom .navbar-toggler[aria-expanded="true"] {
  background-color: transparent !important; /* Prevent background change on expand */
  border: 1px solid #ffffff !important; /* Maintain white border */
}

/* Dark Mode Button */
body.dark-mode .navbar-custom .theme-toggle i {
  color: #ffffff; /* White color for dark mode */
}

body.dark-mode .navbar-custom .theme-toggle i:hover {
  color: #ffcc00; /* Highlight hover color for dark mode */
}

body {
  padding-top: 4.5rem; /* Adjust to match the navbar height */
}

/* --- Reading Progress Bar --- */
.reading-progress-bar {
  position: fixed;
  top: 72px; /* Initial position - will be updated by JavaScript */
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1029; /* Below navbar */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease, top 0.3s ease;
}

.reading-progress-bar.visible {
  opacity: 1;
  visibility: visible;
}

.reading-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0085a1, #00a8cc);
  width: 0%;
  transition: width 0.1s ease-out;
}

/* Dark mode progress bar */
body.dark-mode .reading-progress-bar {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .reading-progress-fill {
  background: linear-gradient(90deg, #ffcc00, #ffd700);
}
