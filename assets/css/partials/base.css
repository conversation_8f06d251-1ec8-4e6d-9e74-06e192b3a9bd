---
layout: null
---

/* --- General --- */

html {
  font-size: 100%;
}

body {
  /* Default font setting */
  font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 1.1rem;
  color: #404040; /* Light mode text color */
  position: relative;
  background-color: #f5f5f5; /* Light mode background */
  overflow-wrap: break-word;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
body > main {
  flex: 1;
}
p {
  line-height: 1.5;
  margin: 1.875rem 0;
  color: inherit !important; /* Ensure consistent text color */
}
/* Font setting for header */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 650;
  line-height: 1.1;
}
h1 {
  font-size: 1.55rem; /* Primary heading */
}
h2 {
  font-size: 1.4rem; /* Secondary heading */
}
h3 {
  font-size: 1.25rem; /* Tertiary heading */
}
h4 {
  font-size: 1.15rem; /* Quaternary heading */
}

h1,
h2,
h3,
h4 {
  margin-top: 1.25rem;
}

a {
  color: #606060; /* Light mode link color */
}
a:hover,
a:focus {
  color: #0085a1; /* Light mode hover color */
  text-decoration: none;
}

hr.small {
  max-width: 6.25rem;
  margin: 1rem auto;
  border-width: 0.25rem;
  border-color: inherit;
  border-radius: 0.1875rem;
}

/* fix in-page anchors to not be behind fixed header */
:target:before {
  content: "";
  display: block;
  height: 3.125rem; /* navbar height */
  margin: -3.125rem 0 0;
}

.hideme {
  display: none;
}

::-moz-selection {
  color: #fff;
  text-shadow: none;
  background-color: #0085a1; /* Light mode selection background */
}
::selection {
  color: #fff;
  text-shadow: none;
  background-color: #0085a1; /* Light mode selection background */
}
img::selection {
  color: #fff;
  background: transparent;
}
img::-moz-selection {
  color: #fff;
  background: transparent;
}

img {
  max-width: 100%;
}

.linked-section {
  padding-top: 3.75rem;
  margin-top: -1.5625rem;
}

/* Comments */

.disqus-comments {
  margin-top: 1.875rem;
}

@media (min-width: 768px) {
  .disqus-comments {
    margin-top: 2.5rem;
  }
}

/* --- Footer --- */

footer {
  background-color: #eaeaea; /* Light mode background */
  color: #404040; /* Light mode text color */
}
footer a {
  color: #606060; /* Light mode link color */
}
footer a:hover {
  color: #0085a1; /* Light mode hover color */
}

/* --- Pagination --- */

.pagination .page-item .page-link {
  font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-transform: uppercase;
  font-size: 0.875rem;
  font-weight: 800;
  letter-spacing: 1px;
  padding: 0.625rem 0.3125rem;
  background-color: #fff;
  border-radius: 0;
  color: #404040; /* Light mode text color */
  padding: 0.75rem 1rem;
}
.pagination .page-item .page-link:hover,
.pagination .page-item .page-link:focus {
  color: #fff;
  border: 1px solid #0085a1; /* Light mode hover color */
  background-color: #0085a1; /* Light mode hover color */
}

/* --- Dark Mode --- */

body.dark-mode {
  background-color: #121212; /* Dark mode background */
  color: #ffffff; /* Dark mode text color */
}

body.dark-mode a {
  color: #b0b0b0; /* Dark mode link color */
}

body.dark-mode a:hover,
body.dark-mode a:focus {
  color: #ffcc00; /* Dark mode hover color */
}

body.dark-mode footer {
  background-color: #2a2a2a; /* Dark mode background */
  border-top: 1px solid #444444; /* Dark mode border */
}

body.dark-mode footer a {
  color: #b0b0b0; /* Dark mode link color */
}

body.dark-mode footer a:hover {
  color: #ffcc00; /* Dark mode hover color */
}

body.dark-mode .pagination .page-item .page-link {
  background-color: #333; /* Dark mode background */
  border-color: #444; /* Dark mode border */
}

body.dark-mode .pagination .page-item .page-link:hover,
body.dark-mode .pagination .page-item .page-link:focus {
  background-color: #444; /* Dark mode hover background */
  color: #ffffff; /* Dark mode hover text color */
  border-color: #ffcc00; /* Dark mode hover border color */
}


/* Added block elements */
.quote-line {
  border-left: 3px solid black;
  padding-left: 1em;
  margin-top: 15px;
}

.quote-line p {
  color: inherit; /* Ensure consistent text color */
}

hr {
    border: none; /* Remove default border */
    height: 1.5px; /* Set thickness */
    background-color: #ddd; /* Light mode color */
    margin: 20px 0; /* Spacing around the <hr> */
  }

/* Dark mode styles */
body.dark-mode .quote-line {
  border-left: 3px solid white; /* Change border color for dark mode */
}

body.dark-mode .quote-line p {
  color: inherit; /* Maintain consistency with parent in dark mode */
}

/* Dark Mode for <hr> */
body.dark-mode hr {
  background-color: #888; /* Dark mode color */
}