// Open the search overlay
document.getElementById('search-toggle').addEventListener('click', function () {
  const searchOverlay = document.getElementById('search-overlay');
  searchOverlay.classList.add('active');
});

// Close the search overlay when the close button is clicked
document.getElementById('close-search').addEventListener('click', function () {
  const searchOverlay = document.getElementById('search-overlay');
  searchOverlay.classList.remove('active');
});

// Close the search overlay when pressing the "Esc" key
document.addEventListener('keydown', function (event) {
  if (event.key === 'Escape') {
    const searchOverlay = document.getElementById('search-overlay');
    searchOverlay.classList.remove('active');
  }
});

// Close the search overlay when clicking outside the search box
document.addEventListener('click', function (event) {
  const searchOverlay = document.getElementById('search-overlay');
  const searchBox = document.querySelector('.search-box');
  if (
    searchOverlay.classList.contains('active') &&
    !searchBox.contains(event.target) &&
    !event.target.closest('.search-toggle')
  ) {
    searchOverlay.classList.remove('active');
  }
});

// Handle search input (placeholder for now)
document.getElementById('search-input').addEventListener('input', function () {
  const query = this.value.toLowerCase();
  console.log(`Search Query: ${query}`); // Replace with actual search logic
});
