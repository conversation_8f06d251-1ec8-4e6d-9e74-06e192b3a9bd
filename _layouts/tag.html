---
layout: post
---

<section class="content-box">
  <h3 style="margin: 0">Tag: {{ page.tag }}</h3>
  <hr / style="margin-bottom: 0px">
</section>

<!-- Load metadata -->
{% assign posts_meta = site.data.posts_meta.list %}
{% assign projects_meta = site.data.projects_meta.list %}

{% assign matched_keys = "" %} <!-- Initialize an empty string to store keys -->

<!-- Find all keys that contain the tag -->
{% for key_tags in site.data.tags %}
  {% assign key = key_tags[0] %}
  {% assign tags = key_tags[1] %}
  {% if tags contains page.tag %}
    {% assign matched_keys = matched_keys | append: key | append: "," %}
  {% endif %}
{% endfor %}

<!-- Convert matched_keys to an array -->
{% assign matched_keys_array = matched_keys | split: "," %}

{% for key in matched_keys_array %}
  <!-- Initialize variables -->
  {% assign post_name = "" %}
  {% assign path = "" %}
  {% assign post_date = "" %}  
  {% assign post_type = "" %}
  {% assign matched = false %}

  <!-- Metadata check from posts -->
  {% for post_info in posts_meta %}
    {% if post_info[1].path == key %}
      {% assign post_name = post_info[1].post_name %}
      {% assign path = post_info[1].path %}
      {% assign post_date = post_info[1].post_date %}
      {% assign post_type = "Post" %}
      {% assign matched = true %}
    {% endif %}
  {% endfor %}

  <!-- Metadata check from projects -->
  {% unless matched %}
    {% for project_info in projects_meta %}
      {% if project_info[1].path == key %}
        {% assign post_name = project_info[1].post_name %}
        {% assign path = project_info[1].path %}
        {% assign post_date = project_info[1].post_date %}
        {% assign post_type = "Project" %}
        {% assign matched = true %}
      {% endif %}
    {% endfor %}
  {% endunless %}

  <!-- Render content only if a match was found -->
  {% if matched %}
    <section class="content-box" style="position: relative; padding-bottom: 15px; padding-top: 20px">
      <p class="item-type" style="font-size: 0.9em">{{ post_type }}</p>
      <h4 class="post-title">
      <a
        href="{{ site.baseurl }}{% link {{ path }} %}"
        >{{ post_name }}</a
      >
      </h4>
      <div class="tags-and-button">
        <p class="item-date" style="font-size: 0.9em">{{ post_date }}</p>
      <a
        href="{{ site.baseurl }}{% link {{ path }} %}"
        class="read-more-btn"
      >
          <i class="fa fa-book"></i> Read More
        </a>
      </div>
    </section>
  {% endif %}
{% endfor %}

<!-- Style to locate data and button in the same row -->
<style>
  .date-and-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    flex-wrap: wrap; /* Allow items to wrap on narrow screens */
  }
</style>
