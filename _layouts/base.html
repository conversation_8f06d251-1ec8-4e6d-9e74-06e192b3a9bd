---
common-css:
  - "/assets/css/main.css"
common-ext-css:
  - href: "https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css"
    sri: "sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh"
  - "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css"
  - "https://fonts.googleapis.com/css?family=Lora:400,700,400italic,700italic"
  - "https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800"
  - "https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css"
common-ext-js:
  - href: "https://code.jquery.com/jquery-3.5.1.slim.min.js"
    sri: "sha256-4+XzXVhsDmqanXGHaHvgh1gMQKX40OUvDEBTu8JcmNs="
  - href: "https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
    sri: "sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo"
  - href: "https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"
    sri: "sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6"
  - href: "https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.js"
  - href: "https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/contrib/auto-render.min.js"
  - href: "https://cdn.jsdelivr.net/npm/katex@0.16.0/contrib/mhchem.min.js"
common-js:
  - "/assets/js/base.js"
  - "/assets/js/search.js"
---

<!DOCTYPE html>
<html lang="{{ page.language | default: site.language | default: 'en' }}">
{% include head.html %}

{% include search.html %}

  <body>
    {% include gtm_body.html %}

    {% include nav.html %}

    {{ content }}

    {% include reading_progress.html %}

    {% include scroll_btn.html %}

    {% include footer.html %}

    {% include footer-scripts.html %}

    <!-- KaTeX Rendering Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function() {
          document.querySelectorAll(".equation_math").forEach(function(el) {
              katex.render(el.textContent, el, {
                  throwOnError: true,
                  trust: true
              });
          });
          document.querySelectorAll(".equation_math_block").forEach(function(el) {
              katex.render(el.textContent, el, {
                  throwOnError: true,
                  displayMode: true,
                  trust: true
              });
          });
      });
    </script>

    <!-- KaTeX Auto-rendering to fix tag of math formula overlapping -->
    <script>
      $(window).on("load resize",function(){
        $(".katex-display").each(function(i,elem){
          var ts=$(elem).find(".tag");
          var eqns=$(elem).find(".base");
          if(ts.length*eqns.length==0)
            return;
          var t=ts[0];
          if(eqns[eqns.length-1].getBoundingClientRect().right>t.getBoundingClientRect().left){
            t.classList.add("tag-overflowed");
          }else if(eqns[0].getBoundingClientRect().left-elem.getBoundingClientRect().left>=t.offsetWidth/2){
            t.classList.remove("tag-overflowed");
          }
        });
      });
    </script>
  </body>
</html>
