---
layout: base
---

<main
  class="{% if page.full-width %} container-fluid {% else %} container-md {% endif %}"
>
  <div class="row">
    <!-- Header Section -->
    <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 order-2 order-md-2 order-lg-1">
      {% include header.html type="page" %}

      <!-- Only show 'latest.html' for larger screens -->
      <div class="d-none d-lg-block">
        {% include latest.html %}
        {% include tags.html %}
      </div>
    </div>

    <!-- Main Content Section -->
    <div class="col-xl-9 col-lg-9 col-md-12 col-sm-12 order-1 order-md-1 order-lg-2">
      {{ content }}

      <!-- Show 'latest.html' under main content for smaller screens -->
      <div class="d-block d-lg-none">
        {% include latest.html %}
        {% include tags.html %}
      </div>
    </div>
  </div>
</main>

<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}
  });
</script>
<script
  type="text/javascript"
  async
  src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-AMS_CHTML"
></script>
