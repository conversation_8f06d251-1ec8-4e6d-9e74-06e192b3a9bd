from bs4 import BeautifulSoup


def fix_img_links(html_soup, new_post_dir, assets_list):
    """Fix the image links in the markdown content.

    Args:
        html_soup (BeautifulSoup): HTML content of the post
        new_post_dir (str): new post directory
        assets_list (list): list of assets in the post directory

    Returns:
        fixed content with proper image links

    """
    # Convert list to set for faster lookups
    assets_set = set(assets_list)

    # Remove the .io part from the new_post_dir
    new_post_dir = new_post_dir.split("github.io")[-1]

    # Find all image tags
    img_tags = html_soup.find_all("img")

    for img in img_tags:
        src = img.get("src")
        # Check if the image source is in the assets set
        for asset in list(assets_set):  # Iterate over a copy of the set
            if asset.replace(" ", "%20") in src:
                # Update the src attribute with the new path
                new_src = new_post_dir + "/content/" + asset
                img["src"] = new_src

                # Remove the asset from the set
                assets_set.remove(asset)
                break  # Exit the loop early as we have already modified the src

    return html_soup


def add_prefix_to_html(html_soup, post_date, post_dir):
    """
    Add prefix to the markdown content.
    ---
    layout: page
    permalink: post_url
    ---
    """

    # Remove the .io part from the new_post_dir
    post_dir = post_dir.split("github.io/")[-1]

    # Create the prefix as a string
    prefix = f"---\nlayout: page\npermalink: {post_dir}/content.html\n---\n\n"

    # Convert the prefix to a BeautifulSoup object
    prefix_soup = BeautifulSoup(prefix, "html.parser")

    # Convert the original soup to a string
    original_html = str(html_soup)

    # Combine the prefix and the original HTML content
    new_content = str(prefix_soup) + original_html

    # Parse the combined content back into a BeautifulSoup object
    new_soup = BeautifulSoup(new_content, "html.parser")

    # Add post date after title tag
    title_tag = new_soup.find("h1")

    # Create the <h6> tag with style
    h6_tag = new_soup.new_tag("h6", style="font-weight: 300; font-size: 0.9em;")
    h6_tag.string = (
        f"Date: {post_date} | Author: Ki-Ung Song"  # Adding text and the post date
    )

    # Insert the <h6> right after the title tag
    title_tag.insert_after(h6_tag)

    return new_soup


def modify_style(html_soup):
    # Find and remove all <style> tags
    for style in html_soup.find_all("style"):
        style.decompose()

    # Fix the class of callout elements
    html_soup = modify_callout_class(html_soup)

    # Remove unnecessary <strong> & </br> tags
    html_soup = remove_unnecessary(html_soup)

    # Modify the font-weight of headers
    html_soup = modify_font_weight(html_soup)

    # Modify the table of contents class names
    html_soup = modify_table_of_content(html_soup)

    # Modify the href tags setting
    html_soup = modify_href(html_soup)

    # Modify the equation tags
    html_soup = modify_equation(html_soup)

    # Modify the options for spacing
    html_soup = modify_basic_space(html_soup)

    # Format the HTML content
    formatted_html = html_soup.prettify()
    html_soup = BeautifulSoup(formatted_html, "html.parser")

    return html_soup


def modify_callout_class(html_soup):
    # Find all <figure> tags
    figures = html_soup.find_all("figure")

    # Iterate through each <figure> tag
    for figure in figures:
        # If the <figure> tag has the class "callout", change it to "notion_callout"
        if "callout" in figure["class"]:
            # Create a new div with the class "notion_callout"
            new_div = html_soup.new_tag("div", **{"class": "notion_callout"})

            # Add class "text" to the second <div> inside the <figure>
            divs = figure.find_all("div")
            if len(divs) > 1:
                divs[1]["class"] = divs[1].get("class", []) + ["text"]

            # Move all contents of the figure to the new div
            new_div.extend(figure.contents)

            # Replace the figure with the new div
            figure.replace_with(new_div)

    return html_soup


def remove_unnecessary(html_soup):
    """
    Remove unnecessary <strong> if it exists in table_of_contents-link class or h1, h2, h3, h4 tags.
    Also remove <br> tags.
    """
    # Find all <strong> tags
    strong_tags = html_soup.find_all("strong")

    # Iterate through each <strong> tag
    for strong in strong_tags:
        # Check if <strong> is within a tag with class 'table_of_contents-link' or within h1, h2, h3, h4 tags
        parent = strong.find_parent()
        if (
            parent and "table_of_contents-link" in parent.get("class", [])
        ) or parent.name in ["h1", "h2", "h3", "h4"]:
            # Unwrap the <strong> tag, removing it but keeping its content
            strong.unwrap()

    # Find and remove all <br/> tags
    for br in html_soup.find_all("br"):
        br.decompose()

    return html_soup


def modify_font_weight(html_soup):
    """
    Modify the font-weight to 650 for every h1, h2, h3 tag in the given soup.
    """
    # Find all h1, h2, and h3 tags
    headers = html_soup.find_all(["h1", "h2", "h3"])

    # Iterate through each header tag
    for header in headers:
        # Set the font-weight to 650
        if "style" in header.attrs:
            # If the style attribute already exists, append the new font-weight
            header["style"] += "; font-weight: 650;"
        else:
            # If the style attribute does not exist, create it
            header["style"] = "font-weight: 650;"

    return html_soup


def modify_table_of_content(html_soup):
    """
    Find class names that end with 'table_of_contents' and replace them with 'table_of_contents'.
    Also, add a "Table of Contents" heading inside each <nav> tag using an <h5> tag.
    """
    # Find all <nav> tags
    nav_tags = html_soup.find_all("nav")
    assert (
        len(nav_tags) == 1
    ), "There should be only one <nav> tag in the HTML content: Table of Contents"

    # Iterate through each <nav> tag
    for nav in nav_tags:
        # Create a <details> tag and set it to initially open
        details = html_soup.new_tag("details")

        # Create a <summary> tag for the heading
        summary = html_soup.new_tag("summary")
        span = html_soup.new_tag(
            "span", style="font-weight: 650; white-space: nowrap;"
        )  # Styling can be applied here
        span.string = "Table of Contents"
        summary.append(span)

        # Copy existing nav contents before clearing
        nav["class"] = ["table_of_contents"]
        nav_contents = list(nav.contents)  # Make a list copy of the contents

        # Clear the nav tag before restructuring
        nav.clear()

        # Append the <summary> and then the copied content to <details>
        div_container = html_soup.new_tag(
            "div", style="padding-top: 10px; padding-bottom: 10px;"
        )
        details.append(summary)
        for content in nav_contents:
            div_container.append(content)
        details.append(div_container)

        # Replace the original nav content with the new <details> structure
        nav.append(details)

    return html_soup


def modify_href(html_soup):
    """
    Modify the "a" tag (href) to add the target attribute.
    If img in "a" tag, then remove this tag and replace it with img tag.
    """
    # Find all <a> tags
    a_tags = html_soup.find_all("a")

    # Iterate through each <a> tag
    for a_tag in a_tags:
        # Check if <a> tag contains an <img> tag
        if a_tag.find("img"):
            # Extract the <img> tag
            img_tag = a_tag.find("img")
            # Replace <a> tag with <img> tag in the soup
            a_tag.replace_with(img_tag)

    return html_soup


def modify_equation(html_soup):
    """
    Modify the equation tags to add the class 'equation' and 'math' to the tags.
    """
    # Find all <mrow> tags and remove them
    for mrow in html_soup.find_all("mrow"):
        mrow.decompose()

    # Find all <span class="mord"> tags and remove them
    for span in html_soup.find_all("span", class_="mord"):
        span.decompose()

    # Find all <span aria-hidden="true" class="katex-html"> tags and remove them
    for span in html_soup.find_all(
        "span", {"aria-hidden": "true", "class": "katex-html"}
    ):
        span.decompose()

    # Find all class "equation-container" or "notion-text-equation-token"
    for tag in html_soup.find_all(
        class_=["equation-container", "notion-text-equation-token"]
    ):
        # Collect all relevant 'katex' elements that contain 'annotation'
        relevant_katex_elements = []
        for katex in tag.find_all(class_="katex"):
            annotation = katex.find("annotation", encoding="application/x-tex")
            if annotation:
                katex.clear()  # Clears all children
                katex.append(annotation.get_text())  # Reinserts the annotation tag

                if tag["class"] == ["equation-container"]:
                    katex["class"] = "equation_math_block"
                elif tag["class"] == ["notion-text-equation-token"]:
                    katex["class"] = "equation_math"

                # Add the desired 'katex' element to the list
                relevant_katex_elements.append(katex)

        # Clear the contents of each 'tag' completely
        tag.clear()

        # Append only the relevant 'katex' elements back to the 'tag'
        for katex in relevant_katex_elements:
            tag.append(katex)

    # Now for newly set equation_math class, wrap it with katex-display class
    for tag in html_soup.find_all(class_="equation-container"):
        for katex in tag.find_all(class_=["equation_math", "equation_math_block"]):
            # Wrap this with class "katex-display"
            new_tag = html_soup.new_tag("span", **{"class": "katex-display"})
            new_tag.append(
                katex.extract()
            )  # Use extract to move the katex element inside new_tag
            tag.append(new_tag)  # Append the new_tag back into the original parent tag

        # Iterate over each tag with the class "equation_math" or "equation_math_block"
        for tag in html_soup.find_all(class_=["equation_math", "equation_math_block"]):
            # Get the existing text from the tag
            original_text = tag.get_text()

            # Replace \frac with \dfrac
            modified_text = original_text.replace("\\frac", "\\dfrac")

            # Update the tag's contents
            tag.string = modified_text

    # Return the modified HTML/XML content
    return html_soup


def modify_basic_space(html_soup):
    # For every tag "li", add class "notion-li"
    for li in html_soup.find_all("li"):
        li["class"] = "notion-li"

    for li in html_soup.find_all("li"):
        details = li.find("details", recursive=False)
        if details:
            # Remove the 'open' attribute to make sure the details are closed
            if details.has_attr("open"):
                del details["open"]
            # Unwrap the <li> tag while retaining its contents
            li.unwrap()

    # For every tag "p", add calss "notion-paragraph"
    for p in html_soup.find_all("p"):
        p["class"] = "notion-paragraph"

    return html_soup
