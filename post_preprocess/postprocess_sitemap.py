import os
from xml.etree.ElementTree import Element, SubElement, tostring
from xml.dom.minidom import parseString
from preprocess import HOME_DIR

PREDEFINE = ["https://kiungsong.github.io/pages/projects/LLM4Finance/content/"]


def generate_sitemap():
    urlset = Element(
        "urlset",
        xmlns="http://www.sitemaps.org/schemas/sitemap/0.9",
        xmlns_xhtml="http://www.w3.org/1999/xhtml",
    )

    # Collect urls
    urls = PREDEFINE

    # Add urls from the posts
    for post in os.listdir(HOME_DIR + "/pages/posts"):
        urls.append(f"https://kiungsong.github.io/pages/posts/{post}/content/")

    # Add predefined urls
    for url in urls:
        url_element = SubElement(urlset, "url")

        loc = SubElement(url_element, "loc")
        loc.text = url

    # Convert the ElementTree to a string
    rough_string = tostring(urlset, "utf-8")

    # Use minidom to prettify the XML
    reparsed = parseString(rough_string)
    pretty_xml = reparsed.toprettyxml(indent="  ")

    # Save to file
    with open("sitemap.xml", "w", encoding="utf-8") as file:
        file.write(pretty_xml)


if __name__ == "__main__":
    generate_sitemap()
