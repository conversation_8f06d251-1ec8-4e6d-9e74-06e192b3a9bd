import os
import shutil
from datetime import datetime
from pathlib import Path
from utils import (
    extract_dir,
    extract_name,
    read_html_file,
    save_html_file,
    get_new_dir,
    update_metadata_yml,
)
from process_html import add_prefix_to_html, fix_img_links, modify_style

HOME_DIR = str(Path(os.path.dirname(os.path.abspath(__file__))).parent)
CACHE_DIR = os.path.dirname(os.path.abspath(__file__)) + "/cache"
ABSOLUTE_ASSET_DIR = f"{HOME_DIR}/pages/posts"
META_FILE_PATH = f"{HOME_DIR}/_data/posts_meta.yml"


def main():
    """
    Under cache directory, there should be a zip file containing the post.
    """
    assert os.path.exists(CACHE_DIR), f"Cache directory not found: {CACHE_DIR}"

    # Make directory properly
    os.makedirs(ABSOLUTE_ASSET_DIR, exist_ok=True)

    # Only one file should be in the cache
    post_list = os.listdir(CACHE_DIR)
    post = [
        os.path.join(CACHE_DIR, post_zip)
        for post_zip in post_list
        if post_zip.endswith(".zip")
    ]
    assert len(post) == 1, f"Multiple files found in cache: {post}"

    # Unzip the file
    print(f"Unzipping {post[0]}")
    os.system(f"unzip -o {post[0]} -d {CACHE_DIR}")

    # Get target files
    post_files = os.listdir(CACHE_DIR)
    html_file = [
        os.path.join(CACHE_DIR, post_file)
        for post_file in post_files
        if post_file.endswith(".html")
    ][0]
    src_dir = html_file.replace(".html", "")

    # Read the md file and extract the directory and name
    html_soup = read_html_file(html_file)
    post_name = extract_name(html_soup)
    post_dir = extract_dir(html_file, post_name)
    post_date = datetime.now().strftime("%m-%d-%Y")

    # Rename the md file and directory
    os.rename(html_file, f"{CACHE_DIR}/content.html")
    if os.path.exists(f"{CACHE_DIR}/content"):
        os.system(f"rm -rf {CACHE_DIR}/content")
    os.rename(src_dir, f"{CACHE_DIR}/content")

    # Update the dir
    html_file = f"{CACHE_DIR}/content.html"
    src_dir = f"{CACHE_DIR}/content"
    assets_list = os.listdir(src_dir)

    # Update the post asset directory
    new_post_dir = get_new_dir(ABSOLUTE_ASSET_DIR, post_dir)

    # Fix md file
    html_soup = add_prefix_to_html(html_soup, post_date, new_post_dir)
    html_soup = fix_img_links(html_soup, new_post_dir, assets_list)
    html_soup = modify_style(html_soup)
    save_html_file(html_file, html_soup)

    # Move the files
    os.makedirs(new_post_dir, exist_ok=True)
    os.system(f"mv {html_file} {new_post_dir}/content.html")
    shutil.copytree(src_dir, f"{new_post_dir}/content", dirs_exist_ok=True)
    os.system(f"rm -r {src_dir}")

    # Update posts_meta.yml
    update_metadata_yml(ABSOLUTE_ASSET_DIR, META_FILE_PATH)


if __name__ == "__main__":
    main()
