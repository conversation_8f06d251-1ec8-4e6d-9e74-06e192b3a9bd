import re
import os
import yaml
from bs4 import Beautiful<PERSON>oup


def read_html_file(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        html_content = file.read()

    # Parse the HTML content
    soup = BeautifulSoup(html_content, "html.parser")

    # Format the HTML content
    formatted_html = soup.prettify()
    html_soup = BeautifulSoup(formatted_html, "html.parser")

    return html_soup


def save_html_file(file_path, html_soup):
    """
    Save the BeautifulSoup object to an HTML file.

    Args:
        file_path (str): Path to save the HTML file.
        html_soup (BeautifulSoup): BeautifulSoup object containing the HTML content.
    """
    with open(file_path, "w", encoding="utf-8") as file:
        file.write(html_soup.prettify())


def get_new_dir(post_asset_dir, post_dir):
    exsting_posts = os.listdir(post_asset_dir)

    # Check if the post directory already exists
    for post in exsting_posts:
        if post.endswith(post_dir):
            post_nums = int(post.split("_")[0])
            new_post_dir = f"{post_asset_dir}/{str(post_nums).zfill(3)}_{post_dir}"
            return new_post_dir

    # If the post directory does not exist, create a new one
    post_nums = len(exsting_posts)
    new_post_dir = f"{post_asset_dir}/{str(post_nums + 1).zfill(3)}_{post_dir}"
    return new_post_dir


def extract_dir(html_file_name, post_name):
    file_name_raw = html_file_name.split("/")[-1].replace(".html", "")

    # Remove unnecessary parts: consecutive alphanumeric characters
    file_name = re.sub(r"\b[a-zA-Z0-9]{16,}\b", "", file_name_raw)

    # Extract the file name
    file_name = file_name.replace(" ", "_")
    file_name = file_name[:-1] if file_name.endswith("_") else file_name

    # Reference the post name
    post_name = post_name.replace(":", "").replace(" ", "_")
    file_name = post_name if file_name in post_name else file_name

    return file_name


def extract_name(html_soup):
    title = html_soup.find("title")
    assert title, "Title not found in the HTML file. Please check the HTML file."
    return title.get_text(strip=True)


def extract_date(html_soup):
    # Find the <h6> tag with the specific text pattern
    h6_tag = html_soup.find("h6", string=lambda text: text and "Date:" in text)
    assert h6_tag, "Date not found in the HTML file. Please check the HTML file."

    # Extract the text and split to get the date
    text_content = h6_tag.get_text()
    # Example text: "Date: 2023-12-28 | Author: Ki-Ung Song"
    date_part = text_content.split("|")[0]
    return date_part.replace("Date:", "").strip()


def update_metadata_yml(post_asset_dir, meta_file_path):
    RELATIVE_ASSET_DIR = "pages/posts"

    # Order subdir via number in prefix with descending order
    post_subdirs = os.listdir(post_asset_dir)
    post_subdirs.sort(key=lambda x: int(x.split("_")[0]), reverse=True)

    # Get files in each subdir
    meta_yml = {"list": {}}
    for post_subdir in post_subdirs:
        subdir = f"{post_asset_dir}/{post_subdir}"
        rel_subdir = f"{RELATIVE_ASSET_DIR}/{post_subdir}"
        files = os.listdir(subdir)

        if "content.html" in files:
            meta_yml["list"][f"{post_subdir}"] = {}
            meta_yml["list"][f"{post_subdir}"]["path"] = f"{rel_subdir}/content.html"
            meta_yml["list"][f"{post_subdir}"]["post_name"] = extract_name(
                read_html_file(f"{subdir}/content.html")
            )
            meta_yml["list"][f"{post_subdir}"]["post_date"] = extract_date(
                read_html_file(f"{subdir}/content.html")
            )

    # Write the metadata to a .yml file to read in Jekyll
    with open(meta_file_path, "w") as f:
        yaml.dump(
            meta_yml, f, allow_unicode=True, default_flow_style=False, sort_keys=False
        )
