import yaml
from preprocess import META_FILE_PATH
from utils import read_html_file, save_html_file

RED_COLOR = "\033[91m"
END_COLOR = "\033[0m"


def add_hyperlink_to_series(series_name: str):
    meta = yaml.load(open(META_FILE_PATH), Loader=yaml.FullLoader)["list"]
    meta = {value["post_name"]: value["path"] for value in meta.values()}

    # Find valid series
    series = [key for key in meta.keys() if series_name in key]
    if len(series) == 0:
        return print(
            f"{RED_COLOR}Series not found for series name: {series_name}{END_COLOR}"
        )
    else:
        print(f"{RED_COLOR}The following series found: {series}{END_COLOR}")
        proceed = input("Want to proceed? (y/n): ")
        if proceed != "y":
            return print(f"{RED_COLOR}Aborted{END_COLOR}")

    # Add hyperlink to series
    for i, post_key in enumerate(series):
        html_soup = read_html_file(meta[post_key])

        # Find the div with class "page-body"
        page_body_div = html_soup.find("div", class_="page-body")

        # Create a <details> tag for the table of hyperlinks
        details = html_soup.new_tag("details", **{"style": "padding-bottom: 10px;"})

        # Create a <summary> tag for the heading
        summary = html_soup.new_tag("summary")
        span = html_soup.new_tag("span", style="font-weight: 650;")
        span.string = f"{series_name} Series"
        summary.append(span)

        # Create a <ul> tag for the list of hyperlinks
        ul = html_soup.new_tag("ul", **{"class": "bulleted-list"})

        for j, new_post_key in enumerate(series):
            li = html_soup.new_tag("li", **{"class": "notion-li"})
            div = html_soup.new_tag("div")
            div.string = new_post_key

            # No hyperlink for the current post
            if i == j:
                u_tag = html_soup.new_tag("u")
                u_tag.string = new_post_key + " - Current Post"
                div.clear()
                div.append(u_tag)
                a = div
            else:
                a = html_soup.new_tag(
                    "a",
                    href="{{ site.baseurl }}{% link " + f"{meta[new_post_key]}" + " %}",
                )
                a.append(div)

            li.append(a)
            ul.append(li)

        # Add to the soup
        details.append(summary)
        details.append(ul)
        page_body_div.insert(0, details)

        # Save the file
        save_html_file(meta[post_key], html_soup)


if __name__ == "__main__":
    add_hyperlink_to_series("Neural Solver Towards Future of Simulation")
