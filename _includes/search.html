<div id="search-overlay" class="search-overlay">
  <div id="baseurl" data-baseurl="{{ site.baseurl }}"></div>
  <div class="search-box">
    <!-- Close Button -->
    <button id="close-search" class="close-search">&times;</button>

    <!-- Search Input -->
    <input
      type="text"
      id="search-input"
      class="search-input"
      placeholder="Search ↵"
    />

    <!-- Search Results -->
    <div id="search-results-container" class="search-results-container">
      <div id="placeholder" class="search-placeholder">
        <div class="search-placeholder-icon">🔍</div>
        <div class="search-placeholder-text">Type a keyword to begin your search.</div>
      </div>
      <div id="no-results" class="no-results" style="display:none">No results found. Please try a different keyword.</div>
      <div id="search-error" class="search-error" style="display:none">Something went wrong. Please try again later.</div>
      <pre id="search-data-display">
        <!-- Search results will be displayed here -->
      </pre>
    </div>
  </div>
</div>


<script src="https://unpkg.com/lunr/lunr.min.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    let lunrIndex;
    let searchData;

    const searchResultsContainer = document.getElementById("search-data-display");
    const placeholder = document.getElementById("placeholder");
    const noResults = document.getElementById("no-results");
    const searchError = document.getElementById("search-error");
    const searchInput = document.getElementById("search-input");

    const baseurl = document.getElementById("baseurl").dataset.baseurl; // Read from data attribute

    // Initially show the placeholder
    placeholder.style.display = "block";
    noResults.style.display = "none";
    searchError.style.display = "none";
    searchResultsContainer.innerHTML = "";

    fetch(`${baseurl}/assets/search.json`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        searchData = data;

        lunrIndex = lunr(function () {
          this.ref("id");
          this.field("title");
          this.field("content");
          this.field("tags");

          searchData.forEach((doc) => {
            // Ensure tags are processed as a string
            doc.tags = Array.isArray(doc.tags) ? doc.tags.join(", ") : "";
            this.add(doc);
          });
        });
      })
      .catch((error) => {
        searchError.style.display = "block";
        searchResultsContainer.innerHTML = `<p>Error loading search data: ${error.message}</p>`;
      });

    searchInput.addEventListener("input", function () {
      const query = searchInput.value.trim();

      // Clear previous results and hide all messages
      searchResultsContainer.innerHTML = "";
      placeholder.style.display = "none";
      noResults.style.display = "none";
      searchError.style.display = "none";

      if (query.length === 0) {
        placeholder.style.display = "block"; // Show placeholder if input is empty
        return;
      }

      try {
        const results = lunrIndex.search(query);

        if (results.length > 0) {
          results.forEach((result) => {
            const item = searchData.find((doc) => doc.id === result.ref);
            if (item) {
              const tags = typeof item.tags === "string"
                ? item.tags // Use the string directly if it's already joined
                : Array.isArray(item.tags)
                ? item.tags.join(", ") // Join the array if it's an array
                : "No tags available"; // Fallback if tags are missing

              // Create search result element
              const resultElement = document.createElement("a");
              resultElement.className = "search-result-box";
              resultElement.href = `${baseurl}/${item.path}`; // Add href to the entire box

              resultElement.innerHTML = `
                <div class="search-item-title">${item.title}</div>
                <div class="search-item-date">${item.date}</div>
                <div class="search-item-tags">Tags: ${tags}</div>
              `;
              searchResultsContainer.appendChild(resultElement);
            }
          });
        } else {
          noResults.style.display = "block"; // Show "No results found" message
        }
      } catch (error) {
        searchError.style.display = "block"; // Show "Search failed" error
      }
    });
  });
</script>
