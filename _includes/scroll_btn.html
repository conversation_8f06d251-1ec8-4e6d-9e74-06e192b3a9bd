<!-- Scroll to <PERSON> Button -->
<button id="scroll-to-top" title="Go to top">
  &#9650;
  <!-- Unicode for an up-pointing triangle -->
</button>

<style>
  #scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    background-color: #e0e0e0; /* Background color */
    color: #404040; /* Arrow color */
    border: none;
    border-radius: 50%; /* Circular button */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Shadow effect */
    cursor: pointer;
    opacity: 0; /* Initially hidden */
    visibility: hidden; /* Ensures no click events when hidden */
    transition: opacity 0.3s ease, visibility 0.3s ease; /* Smooth appearance/disappearance */
    outline: none;
  }

  #scroll-to-top:hover {
    background-color: #d0d0d0; /* Slightly darker on hover */
  }

  body.dark-mode #scroll-to-top {
    background-color: #3f3f3f; /* Dark mode background color */
    color: #eaeaea; /* Dark mode arrow color */
  }

  body.dark-mode #scroll-to-top:hover {
    background-color: #2f2f2f;
  }

  #scroll-to-top.visible {
    opacity: 1; /* Fully visible */
    visibility: visible; /* Allow click events */
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const scrollToTopButton = document.getElementById("scroll-to-top");

    // Show or hide the button with a smooth transition
    window.addEventListener("scroll", () => {
      if (window.scrollY > window.innerHeight / 2) {
        // Add the visible class to make the button appear
        scrollToTopButton.classList.add("visible");
      } else {
        // Remove the visible class to make the button disappear
        scrollToTopButton.classList.remove("visible");
      }
    });

    // Scroll to top when button is clicked
    scrollToTopButton.addEventListener("click", () => {
      window.scrollTo({
        top: 0,
        behavior: "smooth", // Smooth scrolling
      });
    });
  });
</script>
