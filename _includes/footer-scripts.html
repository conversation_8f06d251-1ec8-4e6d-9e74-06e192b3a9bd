{% if layout.common-ext-js %}
  {% for js in layout.common-ext-js %}
    {% include ext-js.html js=js %}
  {% endfor %}
{% endif %}

{% if layout.common-js %}
  {% for js in layout.common-js %}
    <!-- doing something a bit funky here because I want to be careful not to include <PERSON><PERSON><PERSON><PERSON> twice! -->
    {% if js contains 'jquery' %}
      <script>
        if (typeof jQuery == 'undefined') {
          document.write('<script src="{{ js | relative_url }}"></scr' + 'ipt>');
        }
      </script>
    {% else %}
      <script src="{{ js | relative_url }}"></script>
    {% endif %}
  {% endfor %}
{% endif %}

{% if site.site-js %}
  {% for js in site.site-js %}
    <script src="{{ js | relative_url }}"></script>
  {% endfor %}
{% endif %}

{% if page.ext-js %}
  {% for js in page.ext-js %}
    {% include ext-js.html js=js %}
  {% endfor %}
{% endif %}

{% if page.js %}
  {% for js in page.js %}
    <script src="{{ js | relative_url }}"></script>
  {% endfor %}
{% endif %}

<!-- Day-Night Theme Toggle Script -->
<script>
  // Function to toggle the theme
  function toggleTheme() {
    const body = document.body;
    const icon = document.getElementById("theme-icon");

    // Check current theme and toggle
    if (body.classList.contains("dark-mode")) {
      body.classList.remove("dark-mode");
      localStorage.setItem("theme", "light"); // Save preference
      icon.classList.replace("fa-sun", "fa-moon"); // Change icon
    } else {
      body.classList.add("dark-mode");
      localStorage.setItem("theme", "dark"); // Save preference
      icon.classList.replace("fa-moon", "fa-sun"); // Change icon
    }
  }

  // Load saved theme on page load
  document.addEventListener("DOMContentLoaded", () => {
    const savedTheme = localStorage.getItem("theme");
    const body = document.body;
    const icon = document.getElementById("theme-icon");

    if (savedTheme === "dark") {
      body.classList.add("dark-mode");
      icon.classList.replace("fa-moon", "fa-sun");
    }
  });
</script>
