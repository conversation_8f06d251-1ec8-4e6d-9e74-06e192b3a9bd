<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="google-site-verification" content="AoXDsFO-tzOidBE7MwpZ2ONbRCz9Hh8-VyIp_sTTyfo" />

  {% capture pagetitle %}
    {%- if page.share-title -%}
      {{ page.share-title | strip_html | xml_escape }}
    {%- elsif page.title -%}
      {{ page.title | strip_html | xml_escape  }}
    {%- else -%}
      {{ site.title | strip_html | xml_escape }}
    {%- endif -%}
  {% endcapture %}

  {% capture title %}
    {%- if site.title and site.title-on-all-pages and (site.title != pagetitle) -%}
      {{ pagetitle }} | {{ site.title }}
    {%- else -%}
      {{ pagetitle }}
    {%- endif -%}
  {% endcapture %}

  {% capture description %}
    {%- if page.share-description -%}
      {{ page.share-description | strip_html | xml_escape }}
    {%- elsif page.subtitle -%}
      {{ page.subtitle | strip_html | xml_escape }}
    {%- else -%}
      {%- assign excerpt_length = site.excerpt_length | default: 50 -%}
      {{ page.content | strip_html | xml_escape | truncatewords: excerpt_length | strip }}
    {%- endif -%}
  {% endcapture %}

  <title>{{ title }}</title>

  {% capture author %}
    {%- if page.author -%}
      {{  page.author | strip_html }}
    {%- elsif site.author -%}
      {{ site.author }}
    {%- endif -%}
  {% endcapture %}
  {% if author != "" %}
  <meta name="author" content="{{ author }}">
  {% endif %}

  <meta name="description" content="{{ description }}">

  {% if site.mobile-theme-col %}
  <meta name="theme-color" content="{{ site.mobile-theme-col }}">
  {% endif %}

  {% if site.keywords %}
  <meta name="keywords" content="{{ site.keywords }}">
  {% endif %}

  {% if site.rss-description %}
  <link rel="alternate" type="application/rss+xml" title="{{ site.title }}" href="{{ '/feed.xml' | absolute_url }}">
  {% endif %}

  {% include gtag.html %}
  {% include gtm_head.html %}
  {% include google_analytics.html %}

  {% if layout.common-ext-css %}
    {% for css in layout.common-ext-css %}
      {% include ext-css.html css=css %}
    {% endfor %}
  {% endif %}

  {% if layout.common-css %}
    {% for css in layout.common-css %}
      <link rel="stylesheet" href="{{ css | relative_url }}">
    {% endfor %}
  {% endif %}

  {% if site.site-css %}
    {% for css in site.site-css %}
      <link rel="stylesheet" href="{{ css | relative_url }}">
    {% endfor %}
  {% endif %}

  {% if page.ext-css %}
    {% for css in page.ext-css %}
      {% include ext-css.html css=css %}
    {% endfor %}
  {% endif %}
  
  {% if page.css %}
    {% for css in page.css %}
      <link rel="stylesheet" href="{{ css | relative_url }}">
    {% endfor %}
  {% endif %}

  {% if site.title %}
  <meta property="og:site_name" content="{{ site.title }}">
  {% endif %}

  {%- capture img -%}
    {%- if page.share-img -%}
      {{ page.share-img }}
    {%- elsif page.cover-img -%}
      {%- if page.cover-img.first -%}
        {{ page.cover-img[0].first.first }}
      {%- else -%}
        {{ page.cover-img }}
      {%- endif -%}
    {%- elsif page.thumbnail-img -%}
      {{ page.thumbnail-img }}
    {%- elsif site.avatar -%}
      {{ site.avatar }}
    {% endif %}
  {%- endcapture -%}
  {%- assign img=img | strip -%}

  <meta property="og:title" content="{{ title }}">
  <meta property="og:description" content="{{ description }}">

  {% if img != "" %}
  <meta property="og:image" content="{{ img | absolute_url }}">
  {% endif %}

  {% if page.id %}
  <meta property="og:type" content="article">
  {% if author != "" %}
  <meta property="og:article:author" content="{{ author }}">
  {% endif %}
  <meta property="og:article:published_time" content="{{ page.date | date_to_xmlschema }}">
  <meta property="og:url" content="{{ page.url | absolute_url }}">
  <link rel="canonical" href="{{ page.url | absolute_url }}">
  {% else %}
  <meta property="og:type" content="website">
  <meta property="og:url" content="{{ page.url | absolute_url | strip_index }}">
  <link rel="canonical" href="{{ page.url | absolute_url | strip_index }}">
  {% endif %}

  {% if img != "" and img != site.avatar %}
  <meta name="twitter:card" content="summary_large_image">
  {% else %}
  <meta name="twitter:card" content="summary">
  {% endif %}
  <meta name="twitter:site" content="@{{ site.social-network-links.twitter }}">
  <meta name="twitter:creator" content="@{{ site.social-network-links.twitter }}">

  <meta property="twitter:title" content="{{ title }}">
  <meta property="twitter:description" content="{{ description }}">

  {% if img != "" %}
  <meta name="twitter:image" content="{{ img | absolute_url }}">
  {% endif %}

  {% if page.comments and site.staticman.repository and site.staticman.branch %}
  <link rel="stylesheet" href="{{ "/assets/css/staticman.css" | relative_url }}">
  {% endif %}

  {% if page.head-extra %}
    {% for file in page.head-extra %}
      {% include {{ file }} %}
    {% endfor %}
  {% endif %}

</head>
