<nav class="navbar navbar-expand-xl navbar-light fixed-top navbar-custom {% if page.nav-short %}top-nav-short-permanent{% else %}top-nav-regular{% endif %}">

  <!-- Navbar Brand -->
  <div class="d-flex align-items-center">
    {%- if site.title-img -%}
      <a class="navbar-brand navbar-brand-logo" href="{{ '/' | absolute_url }}">
        <img alt="{{ site.title }} Logo" src="{{ site.title-img | relative_url}}" />
      </a>
    {%- elsif site.title -%}
      <a class="navbar-brand" href="{{ '/' | absolute_url }}">{{ site.title }}</a>
    {%- endif -%}

    <!-- Day-Night Toggle Icon -->
    <span class="nav-link theme-toggle" id="theme-toggle" onclick="toggleTheme()" title="Toggle Dark Mode">
      <i class="fa fa-moon" id="theme-icon"></i>
    </span>

    <!-- Search Button -->
    <span class="nav-link search-toggle" id="search-toggle" title="Search" role="button" tabindex="0">
      <i class="fa fa-search" id="search-icon"></i>
    </span>
  </div>

  <!-- Navbar Toggler for Smaller Screens -->
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#main-navbar" aria-controls="main-navbar" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>

  <!-- Collapsible Links -->
  <div class="collapse navbar-collapse" id="main-navbar">
    <ul class="navbar-nav ml-auto">
      {%- for link in site.navbar-links -%}
        {%- if link[1].first %}
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">{{ link[0] }}</a>
            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
              {%- for childlink in link[1] -%}
                {%- for linkparts in childlink %}
                  <a class="dropdown-item" href="{{ linkparts[1] | relative_url }}">{{ linkparts[0] }}</a>
                {%- endfor %}
              {%- endfor %}
            </div>
          </li>
        {% else %}
          <li class="nav-item">
            <a class="nav-link" href="{{ link[1] | relative_url }}">{{ link[0] }}</a>
          </li>
        {%- endif -%}
      {%- endfor %}
    </ul>
  </div>
</nav>

<style>
  /* Reduce spacing between Day-Night Toggle Icon and Search Button */
  .nav-link.theme-toggle {
    margin-left: 5px;
    margin-right: 0px; 
  }

  .nav-link.search-toggle {
    margin-left: -10px;
  }
</style>