<section class="content-box" style="padding-bottom: 5px; padding-top: 15px">
  <h4 style="font-size: 1rem; margin-top: 0px; margin-bottom: 15px">Tags</h4>

  {% assign all_tags = "" %}
  <!-- Loop through each post's tags -->
  {% for tags_dict in site.data.tags %}
    {% assign tags = tags_dict[1] %}
    {% for tag in tags %}
      {% assign all_tags = all_tags | append: tag | append: ", " %}
    {% endfor %}
  {% endfor %}

  <!-- Convert tags into an array -->
  {% assign all_tags = all_tags | split: ", " %}
  {% assign unique_tags = all_tags | uniq %}

  <!-- Initialize dictionary as a string -->
  {% assign tag_count_dict = "" %}

  <!-- Loop through unique tags to build the dictionary -->
  {% for unique_tag in unique_tags %}
    {% if unique_tag != "" %}
      <!-- Count occurrences of the unique tag in all_tags -->
      {% assign tag_count = 0 %}
      {% for tag in all_tags %}
        {% if tag == unique_tag %}
          {% assign tag_count = tag_count | plus: 1 %}
        {% endif %}
      {% endfor %}

      <!-- Add the tag and its count to the dictionary -->
      {% assign tag_count_dict = tag_count_dict | append: unique_tag | append: ":" | append: tag_count | append: ", " %}
    {% endif %}
  {% endfor %}

  <!-- Split the dictionary string into key-value pairs -->
  {% assign tag_count_pairs = tag_count_dict | split: ", " %}

  <!-- Step 3: Transform into a sortable array -->
  {% assign sortable_array = "" %}
  {% for pair in tag_count_pairs %}
    {% assign tag_name = pair | split: ":" | first | strip %}
    {% assign tag_count = pair | split: ":" | last | strip %}
    {% if tag_name != "" %}
      {% assign sortable_array = sortable_array | append: tag_count | append: "::" | append: tag_name | append: "||" %}
    {% endif %}
  {% endfor %}

  <!-- Step 4: Split the sortable array and sort it -->
  {% assign sortable_array = sortable_array | split: "||" %}
  {% assign sorted_array = sortable_array | sort | reverse %}

  <!-- Step 5: Display the top 10 tags -->
  <div class="tags-layout">
    {% for item in sorted_array limit:10 %}
      {% assign tag_count = item | split: "::" | first %}
      {% assign tag_name = item | split: "::" | last %}

      <!-- Display the tag and its count -->
      <a href="{{ site.baseurl }}/tags/{{ tag_name | slugify }}/" class="tag-board">
        <span class="tag-name">{{ tag_name }}</span>
        <span class="tag-count">{{ tag_count }}</span>
      </a>
    {% endfor %}
  </div>

  <a href="{{ site.baseurl }}{% link pages/tags.html%}" class="see-all">
    See All &gt;&gt;
  </a>

</section>
