<header class="content-box">
  <!-- Avatar Section -->
  <div class="avatar-container">
    <div class="avatar-img-border">
      <img
        alt="Navigation bar avatar"
        class="avatar-img"
        src="{{ site.avatar | relative_url }}"
      />
    </div>
  </div>

  <!-- Name and Subtitle Section -->
  <h1 style="font-size: 1.2em; font-weight: 600" class="text-center">
    {{ site.title | strip_html }}
  </h1>
  <div style="font-size: 0.9em" class="text-center">
    {{ site.subtitle | strip_html }}
  </div>

  <!-- Location Section -->
  <div class="location">
    <i class="fa fa-map-marker"></i>
    <span>{{ site.location | default: "Location not provided" }}</span>
  </div>

  <!-- Social Networks Section -->
  {% include social-networks-links.html %}
</header>

<style>
  /* Avatar Styles */
  .avatar-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1.5rem auto; /* Center align horizontally and add bottom margin */
    max-width: 110px !important; /* Enforce fixed size */
  }

  .avatar-container .avatar-img-border {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden; /* Ensure image stays within the circle */
  }

  .avatar-container .avatar-img {
    object-fit: cover; /* Crop and fill the container */
  }

  .location {
    display: flex;
    justify-content: center; /* Center the content horizontally */
    align-items: center; /* Align items vertically */
    font-size: 0.9rem;
    color: #404040;
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .location i {
    margin-right: 0.5rem;
    color: #0085a1; /* Icon color */
  }

  .location span {
    display: inline-block; /* Ensure text remains inline */
  }

  body.dark-mode .location {
    color: #ffffff;
  }

  body.dark-mode .location i {
    color: #ffcc00; /* Icon color in dark mode */
  }

  .text-center {
    text-align: center; /* Center align text */
  }

  /* For margin after social icons */
  .footer-links {
    margin-bottom: 0; /* Reduce or remove bottom margin */
  }
</style>
