<!-- Reading Progress Bar -->
<div id="reading-progress-bar" class="reading-progress-bar">
  <div id="reading-progress-fill" class="reading-progress-fill"></div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const progressBar = document.getElementById('reading-progress-bar');
    const progressFill = document.getElementById('reading-progress-fill');
    const navbar = document.querySelector('.navbar');

    if (!progressBar || !progressFill || !navbar) return;

    // Function to update progress bar position based on navbar height
    function updateProgressBarPosition() {
      const navbarHeight = navbar.offsetHeight;
      progressBar.style.top = navbarHeight + 'px';
    }

    // Initial positioning
    updateProgressBarPosition();

    // Update position on window resize
    window.addEventListener('resize', updateProgressBarPosition);

    // Only show progress bar on post pages (pages with article content)
    const isPostPage = document.querySelector('article') ||
                      document.querySelector('.page-body') ||
                      document.querySelector('main .content-box');

    if (!isPostPage) {
      return; // Don't show progress bar on non-content pages
    }

    // Check if the page has enough content to warrant a progress bar
    const documentHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;

    if (documentHeight <= windowHeight * 1.5) {
      return; // Don't show progress bar for short pages
    }

    let isVisible = false;

    function updateReadingProgress() {
      // Update progress bar position in case navbar height changed
      updateProgressBarPosition();

      // Calculate the reading progress
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Calculate progress percentage
      const scrollableHeight = documentHeight - windowHeight;
      const progress = scrollableHeight > 0 ? (scrollTop / scrollableHeight) * 100 : 0;

      // Update progress bar width
      progressFill.style.width = Math.min(progress, 100) + '%';

      // Show/hide progress bar based on scroll position
      const shouldShow = scrollTop > 100; // Show after scrolling 100px

      if (shouldShow && !isVisible) {
        progressBar.classList.add('visible');
        isVisible = true;
      } else if (!shouldShow && isVisible) {
        progressBar.classList.remove('visible');
        isVisible = false;
      }
    }

    // Throttle scroll events for better performance
    let ticking = false;
    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateReadingProgress);
        ticking = true;
      }
    }

    function handleScroll() {
      ticking = false;
      requestTick();
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Update on resize to recalculate document height
    window.addEventListener('resize', updateReadingProgress, { passive: true });
    
    // Initial update
    updateReadingProgress();
  });
</script>
