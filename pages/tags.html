---
layout: page
---

<section>
  <h3 style="margin-top: 0px; margin-bottom: 20px" id="tags">Tags</h3>

  {% assign all_tags = "" %}
  <!-- Loop through each post's tags -->
  {% for tags_dict in site.data.tags %}
    {% assign tags = tags_dict[1] %}
    {% for tag in tags %}
      {% assign all_tags = all_tags | append: tag | append: ", " %}
    {% endfor %}
  {% endfor %}

  <!-- Convert tags into an array -->
  {% assign all_tags = all_tags | split: ", " %}
  {% assign unique_tags = all_tags | uniq %}

  <!-- Initialize a tag count dictionary as a string -->
  {% assign tag_count_dict = "" %}

  <!-- Initialize dictionary as a string -->
  {% assign tag_count_dict = "" %}

  <!-- Loop through unique tags to build the dictionary -->
  {% for unique_tag in unique_tags %}
    {% if unique_tag != "" %}
      <!-- Count occurrences of the unique tag in all_tags -->
      {% assign tag_count = 0 %}
      {% for tag in all_tags %}
        {% if tag == unique_tag %}
          {% assign tag_count = tag_count | plus: 1 %}
        {% endif %}
      {% endfor %}

      <!-- Add the tag and its count to the dictionary -->
      {% assign tag_count_dict = tag_count_dict | append: unique_tag | append: ":" | append: tag_count | append: ", " %}
    {% endif %}
  {% endfor %}

  <!-- Split the dictionary string into key-value pairs -->
  {% assign tag_count_pairs = tag_count_dict | split: ", " %}

  <!-- Loop through the tag count pairs -->
  <div class="tags-layout">
    {% for tag_count in tag_count_pairs %}
      {% assign name = tag_count | split: ":" | first %}
      {% assign count = tag_count | split: ":" | last %}

      <!-- Display the tag and its count -->
      <a href="{{ site.baseurl }}/tags/{{ name | slugify }}/" class="tag-board">
        <span class="tag-name">{{ name }}</span>
        <span class="tag-count">{{ count }}</span>
      </a>
    {% endfor %}
  </div>

</section>
