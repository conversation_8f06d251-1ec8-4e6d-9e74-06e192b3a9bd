---
layout: post
---

{% for post_info in site.data.posts_meta.list %}
<section class="content-box" style="position: relative; padding-bottom: 20px">
  <h3 class="post-title">
    <a href="{{ site.baseurl }}{% link {{post_info[1].path}}%}"
      >{{post_info[1].post_name}}</a
    >
  </h3>
  <h6 class="post-date">Date: {{post_info[1].post_date}}</h6>

  <!-- Display Tags and Button in One Row -->
  <div class="tags-and-button">
    {% assign key = post_info[1].path %} {% assign tags = site.data.tags[key] %}
    {% if tags %}
    <p class="post-tags">
      <i class="fa fa-tags tag-icon"></i>Tags: 
      {% for tag in tags %} 
        <a href="{{ site.baseurl }}/tags/{{ tag | slugify }}/">{{ tag }}</a>{% if forloop.last == false %}, {% endif %} 
      {% endfor %}
    </p>
    {% else %}
    <p class="post-tags"><i class="fa fa-tags tag-icon"></i>Tags: None</p>
    {% endif %}

    <a
      href="{{ site.baseurl }}{% link {{ post_info[1].path }} %}"
      class="read-more-btn"
    >
      <i class="fa fa-book"></i> Read More
    </a>
  </div>
</section>
{% endfor %}
