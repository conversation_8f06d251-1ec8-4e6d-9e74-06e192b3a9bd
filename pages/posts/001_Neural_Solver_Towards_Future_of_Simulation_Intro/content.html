---
layout: page
permalink: pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content.html
---
<html>
 <head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
  <title>
   Neural Solver Towards Future of Simulation: Intro
  </title>
 </head>
 <body>
  <article class="page sans" id="cc84450e-2e72-43f8-b3af-e30712dc34a8">
   <header>
    <h1 class="page-title" style="font-weight: 650; margin-top: 0px;">
     Neural Solver Towards Future of Simulation: Intro
    </h1>
    <h6 style="font-weight: 300; font-size: 0.9em;">
     Date: 08-09-2024 | Author: Ki-Ung Song
    </h6>
    <p class="notion-paragraph">
    </p>
   </header>
   <div class="page-body">
    <details style="padding-bottom: 10px;">
     <summary>
      <span style="font-weight: 650;">
       Neural Solver Towards Future of Simulation Series
      </span>
     </summary>
     <ul class="bulleted-list">
      <li class="notion-li">
       <a href="{{ site.baseurl }}{% link pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content.html %}">
        <div>
         Neural Solver Towards Future of Simulation: Deep Dive
        </div>
       </a>
      </li>
      <li class="notion-li">
       <a href="{{ site.baseurl }}{% link pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content.html %}">
        <div>
         Neural Solver Towards Future of Simulation: Exploration
        </div>
       </a>
      </li>
      <li class="notion-li">
       <div>
        <u>
         Neural Solver Towards Future of Simulation: Intro - Current Post
        </u>
       </div>
      </li>
     </ul>
    </details>
    <nav class="table_of_contents" id="8f3db4d2-b0fa-4987-80c0-dbe97df0faae">
     <details>
      <summary>
       <span style="font-weight: 650; white-space: nowrap;">
        Table of Contents
       </span>
      </summary>
      <div style="padding-top: 10px; padding-bottom: 10px;">
       <div class="table_of_contents-item table_of_contents-indent-0">
        <a class="table_of_contents-link" href="#8092c350-182e-4818-ad0f-820296462f20">
         What is Simulation?
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#04664166-3512-476f-b869-4f94d3ccc23d">
         Why Simulation?
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#b5c828ca-e455-4518-92a1-f4d5da6c69df">
         How Simulation Works?
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#5b0165c3-c475-438d-8d93-b60570749c72">
         How to Solve Differential Equations?
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-0">
        <a class="table_of_contents-link" href="#d1199db7-ca75-423c-a9c8-c0b223f4d431">
         Neural Solver: DL for Solving Differential Equation
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#f5d4213c-2fb4-40e0-8729-be36a4c6c5d4">
         Vanilla Approach
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#1225eeae-27c8-4ce8-9620-b4b330fc8807">
         Next Steps in Advancing Neural Solvers
        </a>
       </div>
      </div>
     </details>
    </nav>
    <h2 class="" id="8092c350-182e-4818-ad0f-820296462f20" style="font-weight: 650;">
     What is Simulation?
    </h2>
    <div class="notion_callout">
     <div style="font-size:1.5em">
      <span class="icon">
       💡
      </span>
     </div>
     <div class="text" style="width:100%">
      <strong>
       "We Are Most Likely in a Simulation" -Elon Musk-
      </strong>
     </div>
    </div>
    <p class="notion-paragraph" id="1e7967a5-63a7-4c93-8ddf-7f5ce32c8844">
     Everyone has likely heard the term "simulation.” But what exactly is a simulation? And how does this concept, as Elon suggests, challenge our understanding of reality and our existence? Wikipedia describes a simulation as follows.
    </p>
    <blockquote class="block-color-default" id="0a388b78-10a9-4984-801c-8c187a5ea1ca">
     A
     <strong>
      simulation
     </strong>
     is an imitative representation of a process or system that could exist in the real world. -
     <a href="https://en.wikipedia.org/wiki/Simulation">
      Wikipedia
     </a>
     -
    </blockquote>
    <p class="notion-paragraph" id="dd0e3907-744c-4898-9bd1-74827768e331">
     So, how closely can a simulation mimic reality? With the advent of LLMs, we may think of the following scenarios when we hear “simulation”.
    </p>
    <figure class="image" id="aba5a947-ffe5-4a57-bd0f-51134afd4978">
     <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/llm_sim.gif" style="width:480px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2304.03442">
       Generative Agents: Interactive Simulacra of Human Behavior (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="8c4fcc3f-88f7-401b-bb88-1617e70c8738">
     <li class="notion-li" style="list-style-type:disc">
      From Elon's perspective, could our existence be just like the world as above, where each of us acts as objects with individual system prompts given as inputs?
     </li>
    </ul>
    <ul class="bulleted-list" id="7dd4ca2d-a1dd-485a-b3a0-7058a5e715b3">
     <li class="notion-li" style="list-style-type:disc">
      Simulation modeling like the above example is called
      <strong>
       Agent-Based Modeling (ABM)
      </strong>
      .
     </li>
    </ul>
    <ul class="bulleted-list" id="1f41e8c7-60b5-4667-a9ad-eb89d048e208">
     <li class="notion-li" style="list-style-type:disc">
      ABM provides valuable insights into economic and environmental systems:
      <ul class="bulleted-list" id="d599013d-cbb7-4b94-b5ab-01dfa49bd861">
       <li class="notion-li" style="list-style-type:circle">
        Individual agents, such as consumers in an economy or animals in an ecosystem, are modeled with specific behavioral rules.
       </li>
      </ul>
      <ul class="bulleted-list" id="6e605f6a-2e1d-4c51-866d-96fc2a69cf3d">
       <li class="notion-li" style="list-style-type:circle">
        These simulations are useful for studying emergent complex outcomes from simple underlying behaviors.
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="05fe1ef5-a6fd-440b-a43c-a6c4d3f25848">
     ABM is an interesting topic, but today we will focus on traditional simulation methods. They can surprisingly mimic part of our reality and are widely used to solve a variety of real-world problems.
    </p>
    <h3 class="" id="04664166-3512-476f-b869-4f94d3ccc23d" style="font-weight: 650;">
     Why Simulation?
    </h3>
    <ul class="bulleted-list" id="02b5b85a-99dd-41f9-b066-990dbca425f8">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Paradigm Shift of Science
      </strong>
      <figure class="image" id="16329135-1d8d-4302-8562-0340bcd39423" style="text-align:center">
       <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled.png" style="width:480px"/>
       <figcaption>
        <a href="https://www.researchgate.net/publication/361111370_Machine_learning_in_concrete_science_applications_challenges_and_best_practices">
         Machine learning in concrete science: applications, challenges, and best practices
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="62dd9e10-5348-4fe7-bbcd-0bfc82f41352">
       <li class="notion-li" style="list-style-type:circle">
        Nowadays Deep Learning (DL) represents the fourth paradigm: data-driven science.
       </li>
      </ul>
      <ul class="bulleted-list" id="b304977a-0646-40b0-9736-714a15b3b8b0">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         Simulation, the third paradigm, still remains a powerful tool for predicting, analyzing, and understanding complex systems within a data-driven paradigm.
        </strong>
       </li>
      </ul>
      <ul class="bulleted-list" id="cd5f30d4-33ea-422a-8a18-81cc37c14125">
       <li class="notion-li" style="list-style-type:circle">
        New paradigms do not replace existing ones; they absorb and expand upon them, complementing each other.
        <ul class="bulleted-list" id="27ac094a-9b15-47a5-b977-224d783f91b1">
         <li class="notion-li" style="list-style-type:square">
          e.g. Inference process of diffusion models can be viewed in a perspective of simulation. I’ll explain why in this post.
         </li>
        </ul>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="49f5ca1f-693b-4b8b-89ad-509f1db19763">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       How DL can help?
      </strong>
      <ul class="bulleted-list" id="f6d4b2f6-749e-492f-bbf1-91aba5a9b61e">
       <li class="notion-li" style="list-style-type:circle">
        The current simulation framework does not effectively use existing data. Adapting these methods to leverage data-driven advantages is key to scientific development.
       </li>
      </ul>
      <ul class="bulleted-list" id="8997186a-1816-40ef-8ccc-d09b1250ad00">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         DL should improve simulation quality, making it more data-efficient and faster at the same time through active use of existing data.
        </strong>
       </li>
      </ul>
     </li>
    </ul>
    <h3 class="" id="b5c828ca-e455-4518-92a1-f4d5da6c69df" style="font-weight: 650;">
     How Simulation Works?
    </h3>
    <p class="notion-paragraph" id="ae8d9206-0c25-4007-ae11-c8b628153a5d">
     The existing simulation involves creating a mathematical model that approximates real-world processes, often relying on solving various types of differential equations (DEs): ordinary (ODEs), partial (PDEs), and stochastic (SDEs).
    </p>
    <p class="notion-paragraph" id="da2fb1de-0179-42f3-b80c-f0d3b617727f">
     These equations are pivotal in modeling everything from dynamic physical systems and fluid dynamics to financial models and biological processes.
    </p>
    <p class="notion-paragraph" id="312ec421-0b08-4c64-9fa1-ec45fbe6a1ea">
     Here are some detailed examples:
    </p>
    <ul class="bulleted-list" id="68ded25e-390a-4004-bc7c-aebff16fabe2">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Computer Graphics: Frozen
      </strong>
      <ul class="bulleted-list" id="bff867d1-806e-45c6-8f09-7ff0be93a0a5">
       <li class="notion-li" style="list-style-type:circle">
        In the animation film Frozen, the following PDEs are used to simulate realistic snow and ice behavior:
        <a href="https://disneyanimation.com/publications/a-material-point-method-for-snow-simulation/">
         A Material Point Method For Snow Simulation (disneyanimation.com)
        </a>
        <figure class="equation" id="aaeaa6a9-883f-4617-b98f-c9168dc8822c">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            \dfrac{D\rho}{Dt}=0, \quad \rho \dfrac{D \bm{v}}{Dt} = \nabla \cdot \bm{\sigma} + \rho \bm{g}, \quad \bm{\sigma} = \dfrac{1}{J}\dfrac{\partial \Psi}{\partial \bm{F}_E} \bm{F}_E^T
           </span>
          </span>
         </div>
        </figure>
       </li>
      </ul>
      <ul class="bulleted-list" id="c9f5ae81-a845-408f-81e0-0c11bc910592">
       <li class="notion-li" style="list-style-type:circle">
        These equations may look complicated at first glance (and indeed they are), but don't worry. You don't need to understand them. The key takeaway is that solving the PDEs results in stunning graphics that bring the scenes to life:
        <figure class="image" id="a202a5ae-7035-4215-86f9-e94f487f9fc8" style="text-align:center">
         <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/snow_frozen.gif" style="width:480px"/>
         <figcaption>
          <a href="https://www.youtube.com/watch?v=O0kyDKu8K-k&amp;ab_channel=sungkwanpark">
           Disney's Frozen   A Material Point Method For Snow Simulation (youtube.com)
          </a>
         </figcaption>
        </figure>
       </li>
      </ul>
      <ul class="bulleted-list" id="ff1152fc-19b7-4989-a8b0-4185f7c800fd">
       <li class="notion-li" style="list-style-type:circle">
        For those who are interested in the PDEs, click the toggle below for details.
         <details>
          <summary>
           Details
          </summary>
          <ul class="bulleted-list" id="1f55b3cc-28a6-4a2e-940b-a8c3f809d0d9">
           <li class="notion-li" style="list-style-type:disc">
            Snow body’s deformation can be described as a mapping from its undeformed configuration
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{X}
             </span>
            </span>
            to its deformed configuration
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{x}
             </span>
            </span>
            by
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{x} = φ(\bm{X})
             </span>
            </span>
            , which yields the deformation gradient
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{F} = ∂φ/∂\bm{X}
             </span>
            </span>
            .
           </li>
          </ul>
          <ul class="bulleted-list" id="553fe122-82ba-421d-aadd-76837d4f07cf">
           <li class="notion-li" style="list-style-type:disc">
            Deformation
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              φ(\bm{X})
             </span>
            </span>
            changes according to conservation of mass, conservation of momentum and the elasto-plastic constitutive relation.
           </li>
          </ul>
          <ul class="bulleted-list" id="07e0a084-513e-401c-a33b-56eab7e5fc20">
           <li class="notion-li" style="list-style-type:disc">
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              ρ
             </span>
            </span>
            is density,
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              t
             </span>
            </span>
            is time,
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{v}
             </span>
            </span>
            is velocity,
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{σ}
             </span>
            </span>
            is the Cauchy stress,
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{g}
             </span>
            </span>
            is the gravity,
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              Ψ
             </span>
            </span>
            is the elasto-plastic potential energy density,
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{F}_E
             </span>
            </span>
            is the elastic part of the deformation gradient
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              \bm{F}
             </span>
            </span>
            and
            <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
             <span class="equation_math">
              J = \det(\bm{F})
             </span>
            </span>
            .
           </li>
          </ul>
          <ul class="bulleted-list" id="19ab8ad5-136b-49a4-8437-249156523cd6">
           <li class="notion-li" style="list-style-type:disc">
            The main idea to solve this, called material point method, is to use particles (material points) to track mass, momentum and deformation gradient.
            <ul class="bulleted-list" id="99362b60-ea9d-4d57-b820-893b73a4e0e6">
             <li class="notion-li" style="list-style-type:circle">
              Specifically, each particle
              <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
               <span class="equation_math">
                p
               </span>
              </span>
              holds position
              <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
               <span class="equation_math">
                \bm{x}_p=(x_p, y_p, z_p)
               </span>
              </span>
              , velocity
              <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
               <span class="equation_math">
                \bm{v}_p
               </span>
              </span>
              , mass
              <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
               <span class="equation_math">
                m_p
               </span>
              </span>
              , and deformation gradient
              <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
               <span class="equation_math">
                \bm{F}_p
               </span>
              </span>
              .
             </li>
            </ul>
           </li>
          </ul>
         </details>
       </li>
      </ul>
      <ul class="bulleted-list" id="8f749fdc-b321-4396-8912-fec86942524d">
       <li class="notion-li" style="list-style-type:circle">
        More surprisingly, this concept is even applicable to real nature. For an in-depth exploration, check out the following:
        <a href="https://medium.com/penn-engineering/the-snow-graphics-in-frozen-can-predict-the-mechanics-of-real-avalanches-57a453b3752c">
         The Snow Graphics in ‘Frozen’ Can Predict the Mechanics of Real Avalanches | by Penn Engineering
        </a>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="3d8d2651-8cda-4ee6-b12f-fe4f07398c39">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Computational Fluid Dynamics (CFD): Car Design
      </strong>
      <figure class="image" id="e9065c72-163e-4840-bc10-12e3d76ff810" style="text-align:center">
       <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/cfd_car.gif" style="width:384px"/>
       <figcaption>
        <a href="https://www.quora.com/What-is-computational-fluid-dynamics">
         What is computational fluid dynamics? - Quora
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="daa3fe49-9399-492d-bd31-f43b7ec342e5">
       <li class="notion-li" style="list-style-type:circle">
        In car design, CFD simulations use PDEs to model airflow around the vehicle, optimizing for aerodynamic efficiency and reducing drag. This enhances performance, fuel efficiency, and stability.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="a007d041-5132-4ddf-9731-d8c723a99172">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Molecular Dynamics (MD): Drug Discovery
      </strong>
      <figure class="image" id="b7a8abdf-d2f5-431d-bb4b-60a15528d173" style="text-align:center">
       <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/md.gif" style="width:576px"/>
       <figcaption>
        <a href="https://bslgroup.hosted.uark.edu/research.shtml">
         BioSimLab - Research (uark.edu)
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="28467b78-4230-458e-a69e-80d73ce1d555">
       <li class="notion-li" style="list-style-type:circle">
        In drug discovery, MD simulations are used to model the interactions between atoms and molecules. This helps in understanding the behavior of biological systems at the molecular level and aids in the design of new drugs.
       </li>
      </ul>
      <ul class="bulleted-list" id="5b7abfa6-024b-47a6-8214-22bedb9ddb6a">
       <li class="notion-li" style="list-style-type:circle">
        Although DL models like AlphaFold are revolutionizing this domain, the predicted structure itself is not sufficient. It becomes a good starting point for further simulations as introduced above.
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="8c592618-b8d4-4219-9c77-414f88841f32">
     <strong>
      As seen in the examples above, simulations based on equations help solve problems by mimicking real-world phenomena in various fields. The naturally following question is how to solve these differential equations.
     </strong>
    </p>
    <h3 class="" id="5b0165c3-c475-438d-8d93-b60570749c72" style="font-weight: 650;">
     How to Solve Differential Equations?
    </h3>
    <ul class="bulleted-list" id="42d7d234-80e8-4f02-bf64-2552c851e191">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Finite Difference Method (FDM)
      </strong>
      <ul class="bulleted-list" id="83e5d872-4458-4bd2-91f4-b95b9ccf1ac1">
       <li class="notion-li" style="list-style-type:circle">
        FDM is a numerical technique for solving differential equations by approximating them with difference equations. This is particularly effective for solving problems over structured grids, widely used in heat transfer and fluid dynamics.
       </li>
      </ul>
      <ul class="bulleted-list" id="6918666e-fd52-483a-b2c2-49f43ae18d02">
       <li class="notion-li" style="list-style-type:circle">
        The basic idea behind FDM is to replace continuous derivatives with discrete approximations:
        <figure class="equation" id="71200dbf-e26f-46ba-ae72-f72bdf4ebf40">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            \dfrac{\partial u}{\partial x} \approx \dfrac{u(x+h) - u(x)}{h}, \quad \dfrac{\partial^2 u}{\partial x^2} \approx \dfrac{u(x+h) - 2u(x) + u(x-h)}{h^2}
           </span>
          </span>
         </div>
        </figure>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="643b6b33-df32-4d7d-8480-965ea2a93351">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Euler Method
      </strong>
      <ul class="bulleted-list" id="bf897137-91ba-4b0a-8909-49fe3f1667f4">
       <li class="notion-li" style="list-style-type:circle">
        The Euler Method can be seen as a direct application of the FDM. Despite its simplicity, it and it variants can be applied to many kinds of DEs.
       </li>
      </ul>
      <ul class="bulleted-list" id="3f7fd825-d649-4bee-89ee-2294bb9e485c">
       <li class="notion-li" style="list-style-type:circle">
        Given the following simple form of ODE
        <figure class="equation" id="abc99b12-4d75-4b11-b9a7-19de6ec58694">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            \dfrac{dy}{dt} = f(t, y(t))
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="448599af-41c4-4b80-a0c1-3984e8b73cad">
         where
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           t\in[0,1]
          </span>
         </span>
         and notation as
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_0=y(0)
          </span>
         </span>
         ,
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_n=y(t_n)
          </span>
         </span>
         , and
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_N=y(1)
          </span>
         </span>
         .
        </p>
        <p class="notion-paragraph" id="6b9f1604-2596-4d8e-a20b-db6fd0f36a53">
         The Euler method can be expressed as
        </p>
        <figure class="equation" id="c1977dcd-4b6b-4e61-8c61-fd0a8a1da249">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            y_{n+1} = y_n + hf(t_n, y_n)
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="41803bc3-870a-4f34-9b99-a8d2027cc385">
         where
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           h
          </span>
         </span>
         is a step-size, i.e.
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           t_{n+1}-t_n=h=1/N
          </span>
         </span>
         .
        </p>
        <p class="notion-paragraph" id="d9d17df5-b6b0-45e1-ae3a-1bf5efec1bfd">
         Now we can solve this ODE starting from the initial point
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_0
          </span>
         </span>
         . By iteratively applying the Euler method, we compute the values of
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y
          </span>
         </span>
         at discrete points
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           t_n
          </span>
         </span>
         until we reach
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_N
          </span>
         </span>
         .
        </p>
        <ul class="bulleted-list" id="4b301885-da80-43d1-bd92-184be67c6b65">
         <li class="notion-li" style="list-style-type:square">
          This process involves calculating each subsequent value
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            y_{n+1}
           </span>
          </span>
          using the current value
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            y_n
           </span>
          </span>
          and the function
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            f(t_n, y_n)
           </span>
          </span>
          , progressively moving from
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            t_0
           </span>
          </span>
          to
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            t_N
           </span>
          </span>
          .
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="4d7a1c88-5050-40ed-9231-9a35fc49e98c">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         Backward Euler method
        </strong>
        <p class="notion-paragraph" id="7a8751f7-8911-49db-b1cb-debc78ed9b84">
         Considering the following approximation:
        </p>
        <figure class="equation" id="64b479dc-e31b-40f6-8fac-63363f86bbf9">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            y_{n+1}-y_{n}=\int_{t_{n}}^{t_{n+1}}f(t,y(t)) dt \approx hf(t_{n+1}, y_{n+1})
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="24c041a9-b0b2-4fdc-bfc0-f3d004ef3059">
         We can derive the Euler method in reverse direction:
        </p>
        <figure class="equation" id="641d8e2b-07a2-4401-9ac8-1ac965107e33">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            y_{n}=y_{n+1} - hf(t_{n+1}, y_{n+1})
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="0f68fe93-842f-4ce4-aefd-55fece1d745f">
         This implies that we can solve the ODE in the reverse direction by starting from
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_{N}
          </span>
         </span>
         and iteratively applying the formula to compute previous values
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           y_{n}
          </span>
         </span>
         , moving backward from
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           t_{N}
          </span>
         </span>
         to
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           t_{0}
          </span>
         </span>
         .
        </p>
        <ul class="bulleted-list" id="799b56d9-0d4d-4390-a9d8-d89ecde31e55">
         <li class="notion-li" style="list-style-type:square">
          Solving ODEs in reverse will be important in future content. Please remember.
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="f91e0474-1e50-4ca4-8185-d3e951396832">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         Euler–Maruyama method
        </strong>
        <p class="notion-paragraph" id="dfedbb28-5dac-42f8-90ce-dd15126e28cd">
         Those familiar with diffusion models should recognize the following SDE:
        </p>
        <figure class="equation" id="2d2d9c5d-4095-4100-947c-e37506f185ea">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            dx = [f_t(x) - g_t^2  \nabla_x \log p_t(x)]dt + g_t dw
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="fb9e54bf-7551-4fa7-bcfe-dfb2c592f830">
         How to solve this kind of SDE? The Euler–Maruyama method, a variant of the Euler Method adapted for solving SDEs, provides the simple solving process:
        </p>
        <figure class="equation" id="60902314-c943-4dac-af2d-c5d2967cb1b5">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            x_{t-1} = x_t - \gamma[f_t(x_t) - g_t^2  \nabla_{x_t} \log p_t(x_t)] + \sqrt{\gamma}g_tz
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="1e88fac0-38b5-4fd2-8351-4d11c842c7f9">
         where
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           \gamma
          </span>
         </span>
         is a step-size and
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           z \sim \mathcal{N}(0,1)
          </span>
         </span>
         .
        </p>
        <ul class="bulleted-list" id="4bffb3bb-408f-41d9-9410-bb5baf042491">
         <li class="notion-li" style="list-style-type:square">
          Note that this is how inference in diffusion models works.
         </li>
        </ul>
        <ul class="bulleted-list" id="6ca4c93b-f2a5-4cae-8f9e-97c048d6dd26">
         <li class="notion-li" style="list-style-type:square">
          Recent improvements in inference speed for diffusion models are akin to faster DL-based SDE solving. In this context, we can say DL already has a strong relationship with simulation theories.
         </li>
        </ul>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="a96dd737-259f-4e9d-b809-c446f2dddb19">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Finite Element Method (FEM)
      </strong>
      <ul class="bulleted-list" id="e3fb0229-695d-4f91-87ad-98410c9bc7b0">
       <li class="notion-li" style="list-style-type:circle">
        FEM is another commonly used method to solve various DEs. It divides complex systems into smaller, simpler components known as finite elements, also referred to as the mesh method.
        <ul class="bulleted-list" id="e626fe93-ec21-4da6-8390-c67ce9976665">
         <li class="notion-li" style="list-style-type:square">
          Example of a mesh grid in FEM:
          <figure class="image" id="57ef64cc-d8f2-498d-8c33-6f0e91a37239" style="text-align:center">
           <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled 1.png" style="width:288px"/>
           <figcaption>
            <a href="https://en.wikipedia.org/wiki/Finite_element_method">
             Finite element method - Wikipedia
            </a>
           </figcaption>
          </figure>
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="bb86007a-38a7-48d2-bd1e-2a3e482ec27e">
       <li class="notion-li" style="list-style-type:circle">
        FEM aims to approximate the exact solution
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          u
         </span>
        </span>
        of given PDE as a linear combination of basis (trial) functions
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \{\phi_i\}_{i=1}^N
         </span>
        </span>
        :
        <figure class="equation" id="0334fd50-73ed-4359-bdfb-7382d765fc0f">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            u \approx \tilde{u}(x) = \sum_{i=1}^N c_i \phi_i(x)
           </span>
          </span>
         </div>
        </figure>
        <ul class="bulleted-list" id="24ca9fc9-1f2d-4ee9-ae17-a8f7011f2c5d">
         <li class="notion-li" style="list-style-type:square">
          i.e. Basis functions are used to approximate the solution over each finite element.
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="e1317c6a-c281-4849-8da1-5e309702815b">
       <li class="notion-li" style="list-style-type:circle">
        What do basis functions look like?
        <p class="notion-paragraph" id="71fb3fb7-9212-4987-b21f-4bc50218030f">
         These functions are typically chosen for their simplicity and ability to accurately represent the solution within an element. Common basis functions include linear, quadratic, and cubic polynomials.
        </p>
        <ul class="bulleted-list" id="da5f11f3-38ac-4be8-a3de-c63764767c6e">
         <li class="notion-li" style="list-style-type:square">
          1D example: Hat function
          <p class="notion-paragraph" id="8ecdbca5-a51b-4202-8136-7bda330deb4b">
           One common type of basis function in 1D is the Hat function, which is piecewise linear. The Hat function is defined such that it is equal to 1 at a specific node and 0 at adjacent nodes, forming a 'hat' shape.
          </p>
          <figure class="image" id="7f354f39-a715-441e-856c-99e57d3aa04c">
           <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled 2.png" style="width:528px"/>
           <figcaption>
            <a href="https://teachbooks.tudelft.nl/computational-modelling/introduction/shape.html#">
             2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl)
            </a>
           </figcaption>
          </figure>
          <p class="notion-paragraph" id="255ee51b-6e66-42fd-90c5-66acff86b898">
           By finding the proper linear combination, we can visually verify that
           <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
            <span class="equation_math">
             \bar{\Phi} = \sum_i N_i
            </span>
           </span>
           can approximate the given
           <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
            <span class="equation_math">
             \Phi
            </span>
           </span>
           properly:
          </p>
          <figure class="image" id="304b423f-eede-476e-82da-4529eedbee8b">
           <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled 3.png" style="width:432px"/>
           <figcaption>
            <a href="https://math.stackexchange.com/questions/4577765/interpolation-in-finite-element-method">
             partial differential equations - Interpolation in Finite Element Method - Mathematics Stack Exchange
            </a>
           </figcaption>
          </figure>
         </li>
        </ul>
        <ul class="bulleted-list" id="28b742f6-084f-46ef-84b6-1ac8d9e52d88">
         <li class="notion-li" style="list-style-type:square">
          2D example:
          <figure class="image" id="669c14a8-ddf3-4672-a6fb-3abe47b04144">
           <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled 4.png" style="width:480px"/>
           <figcaption>
            <a href="https://teachbooks.tudelft.nl/computational-modelling/introduction/shape.html#">
             2.5. Elements and shape functions — CiTG Jupyter Book template (tudelft.nl)
            </a>
           </figcaption>
          </figure>
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="b25f8adf-1f73-4c43-a723-ecaad05da04d">
       <li class="notion-li" style="list-style-type:circle">
        The FEM method can be used with the FDM to address more complex and time-dependent PDEs.
        <ul class="bulleted-list" id="ef177825-6582-4754-816c-b833c92fa484">
         <li class="notion-li" style="list-style-type:square">
          Combining both methods allows for efficient management of intricate geometries and dynamic systems.
         </li>
        </ul>
        <ul class="bulleted-list" id="ca25fffe-5e31-4993-a51e-59cda9ecf675">
         <li class="notion-li" style="list-style-type:square">
          FEM offers flexible spatial discretization and FDM provides simple time integration techniques, creating a strong framework for simulating various physical phenomena.
         </li>
        </ul>
       </li>
      </ul>
     </li>
    </ul>
    <div class="notion_callout">
     <div style="font-size:1.5em">
      <span class="icon">
       💡
      </span>
     </div>
     <div class="text" style="width:100%">
      <strong>
       Summarizing the content so far, the direction towards better simulation using DL ultimately leads to utilizing DL for improved differential equation solving.
      </strong>
     </div>
    </div>
    <hr id="9896dd6b-523a-4d14-a52a-a5c506a6b18a"/>
    <h2 class="" id="d1199db7-ca75-423c-a9c8-c0b223f4d431" style="font-weight: 650;">
     Neural Solver: DL for Solving Differential Equation
    </h2>
    <blockquote class="" id="9840f957-b4cf-4408-8c84-b5639e69e48b">
     Neural solvers are DL-based frameworks designed to solve differential equations efficiently and accurately. By training and utilizing existing data from these equations, they aim to predict solutions to complex problems that are computationally intensive for traditional methods.
    </blockquote>
    <p class="notion-paragraph" id="62532161-c781-45a4-99e7-aaccef8d6c65">
     <strong>
      Trend of Accepted Papers
     </strong>
    </p>
    <p class="notion-paragraph" id="04449423-017a-4c8a-b29a-f5ffbd7dbd77">
     In recent years, the number of accepted papers on neural solvers has significantly increased. It reflects a growing interest within the scientific community. This trend highlights the potential of DL in transforming traditional problem-solving methods in mathematics and engineering.
    </p>
    <table class="simple-table" id="aebbc5df-9ef7-4caf-a79d-031e6e6b5a2f">
     <thead class="simple-table-header">
      <tr id="e0db930b-cd38-4eb3-8514-4ccf0d496b75">
       <th class="simple-table-header-color simple-table-header" id="OaCp" style="width:132.4px">
       </th>
       <th class="simple-table-header-color simple-table-header" id="D`\d" style="width:132.4px">
        <strong>
         2021
        </strong>
       </th>
       <th class="simple-table-header-color simple-table-header" id="@iuG" style="width:132.3984375px">
        <strong>
         2022
        </strong>
       </th>
       <th class="simple-table-header-color simple-table-header" id="OguD" style="width:132.4px">
        <strong>
         2023
        </strong>
       </th>
       <th class="simple-table-header-color simple-table-header" id="my@e" style="width:132.4px">
        <strong>
         2024
        </strong>
       </th>
      </tr>
     </thead>
     <tbody>
      <tr id="f8f9bc95-10d3-407e-8acb-9caa84cf4576">
       <th class="simple-table-header-color simple-table-header" id="OaCp" style="width:132.4px">
        <strong>
         NeurIPS
        </strong>
       </th>
       <td class="" id="D`\d" style="width:132.4px">
        <strong>
         11
        </strong>
       </td>
       <td class="" id="@iuG" style="width:132.3984375px">
        <strong>
         17
        </strong>
       </td>
       <td class="" id="OguD" style="width:132.4px">
        <strong>
         33
        </strong>
       </td>
       <td class="" id="my@e" style="width:132.4px">
        <strong>
         -
        </strong>
       </td>
      </tr>
      <tr id="2e13d3aa-7446-48c6-8f02-071598777458">
       <th class="simple-table-header-color simple-table-header" id="OaCp" style="width:132.4px">
        <strong>
         ICLR
        </strong>
       </th>
       <td class="" id="D`\d" style="width:132.4px">
        <strong>
         3
        </strong>
       </td>
       <td class="" id="@iuG" style="width:132.3984375px">
        <strong>
         6
        </strong>
       </td>
       <td class="" id="OguD" style="width:132.4px">
        <strong>
         11
        </strong>
       </td>
       <td class="" id="my@e" style="width:132.4px">
        <strong>
         9
        </strong>
       </td>
      </tr>
      <tr id="3e69d2d3-0504-495c-a4e0-c3c231621ba0">
       <th class="simple-table-header-color simple-table-header" id="OaCp" style="width:132.4px">
        <strong>
         ICML
        </strong>
       </th>
       <td class="" id="D`\d" style="width:132.4px">
        <strong>
         3
        </strong>
       </td>
       <td class="" id="@iuG" style="width:132.3984375px">
        <strong>
         3
        </strong>
       </td>
       <td class="" id="OguD" style="width:132.4px">
        <strong>
         21
        </strong>
       </td>
       <td class="" id="my@e" style="width:132.4px">
        <strong>
         17
        </strong>
       </td>
      </tr>
     </tbody>
    </table>
    <ul class="bulleted-list" id="e23153b2-9ab7-4836-96f5-54ee7761b7df">
     <li class="notion-li" style="list-style-type:disc">
      The table is based on a survey conducted as of July 10, 2024, so it does not reflect status regarding NeurIPS 2024.
     </li>
    </ul>
    <h3 class="" id="f5d4213c-2fb4-40e0-8729-be36a4c6c5d4" style="font-weight: 650;">
     Vanilla Approach
    </h3>
    <p class="notion-paragraph" id="a0549abc-8acd-4483-896d-44dc6b62d8f7">
     <strong>
      Why don't we apply DL to the simulation data directly then? Shouldn't it work?
     </strong>
    </p>
    <p class="notion-paragraph" id="16286a8e-dd3d-462c-887f-92d8a0c58139">
     The content available at
     <a href="https://physicsbaseddeeplearning.org/supervised-airfoils.html">
      Supervised training for RANS flows around airfoils — Physics-based Deep Learning
     </a>
     provides an excellent example of this and so it will be introduced here.
    </p>
    <ul class="bulleted-list" id="c1725152-a0f8-4290-bdd1-fcc85cf032e4">
     <li class="notion-li" style="list-style-type:disc">
      Training data:
      <strong>
       Reynolds-Averaged Navier Stokes(RANS) around Airfoils
      </strong>
      <figure class="image" id="131319f0-38dc-4bc0-9035-2b205079777a">
       <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled 5.png" style="width:576px"/>
       <figcaption>
        <a href="https://physicsbaseddeeplearning.org/supervised-airfoils.html">
         Supervised training for RANS flows around airfoils — Physics-based Deep Learning
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="db715458-cc70-4b2f-b403-62c6578c05d0">
       <li class="notion-li" style="list-style-type:circle">
        RANS approach is a fundamental method used in computational fluid dynamics for simulation.
       </li>
      </ul>
      <ul class="bulleted-list" id="01d0e7dc-3b2d-4f8b-8542-a04f49473a8b">
       <li class="notion-li" style="list-style-type:circle">
        Underlying dynamics: Navier-Stokes Equation
        <figure class="equation" id="92093101-70b6-476f-aa33-cac9f100abfe">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            \dfrac{\partial \mathbf{u}}{\partial{t}} + \mathbf{u} \cdot \nabla \mathbf{u} = - \dfrac{1}{\rho} \nabla p + \nu \Delta \mathbf{u} + \mathbf{g} \quad s.t. \quad \nabla \cdot \mathbf{u} = 0
           </span>
          </span>
         </div>
        </figure>
        <p class="notion-paragraph" id="cd710093-a74a-4190-bddb-cef859f16f7c">
         where
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           \mathbf{u}
          </span>
         </span>
         is flow velocity,
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           p
          </span>
         </span>
         is pressure,
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           \rho
          </span>
         </span>
         is mass density,
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           \nu
          </span>
         </span>
         is diffusion constant for viscosity, and
         <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
          <span class="equation_math">
           \mathbf{g}
          </span>
         </span>
         is body acceleration (e.g.  gravity, inertia acceleration).
        </p>
        <ul class="bulleted-list" id="acec36d7-5344-4c94-aca7-e594df1a60da">
         <li class="notion-li" style="list-style-type:square">
          The Navier-Stokes equation, known for its complexity, poses significant challenges in fluid dynamics and is one of the seven Millennium Prize Problems.
         </li>
        </ul>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="720915c5-f5f4-4f37-8976-d70c7bd95700">
     <li class="notion-li" style="list-style-type:disc">
      DL model: Simple U-net shape architecture with naive supervised loss
      <figure class="image" id="2fe6425a-519e-4416-a95f-579228c45873">
       <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/Untitled 6.png" style="width:617px"/>
       <figcaption>
        <a href="https://physicsbaseddeeplearning.org/supervised-airfoils.html">
         Supervised training for RANS flows around airfoils — Physics-based Deep Learning
        </a>
       </figcaption>
      </figure>
      <figure class="equation" id="73cb4195-ee24-4e67-a695-6c28c7c30e74">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          \text{arg min}_{\theta} \sum_i ( f_\theta(x_i)-y_i )^2
         </span>
        </span>
       </div>
      </figure>
      <p class="notion-paragraph" id="a620c05d-b619-40b2-a004-d019967329b6">
       where
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         x_i
        </span>
       </span>
       are the boundary conditions shown on the left side of the figure, and the target
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         y_i
        </span>
       </span>
       are the pre-computed field quantities like pressure and velocity on the right side.
      </p>
     </li>
    </ul>
    <ul class="bulleted-list" id="3b5c2494-bff8-4396-9b7f-b8a3b86f046f">
     <li class="notion-li" style="list-style-type:disc">
      Results: Not Optimal
      <figure class="image" id="106efe6a-1aa2-4aac-8977-756882f0c906">
       <img src="/pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content/vanilla_result.jpg" style="width:1034px"/>
       <figcaption>
        <a href="https://physicsbaseddeeplearning.org/supervised-airfoils.html">
         Supervised training for RANS flows around airfoils — Physics-based Deep Learning
        </a>
       </figcaption>
      </figure>
      <p class="notion-paragraph" id="8ecd63f5-a4c3-4ee2-bf22-b8ae2cff0698">
       The vanilla DL approach shows limitations in capturing the complex dynamics of around airfoils, with predictions often not aligning closely with physical data, suggesting a need for more advanced models or training methods.
      </p>
     </li>
    </ul>
    <h3 class="" id="1225eeae-27c8-4ce8-9620-b4b330fc8807" style="font-weight: 650;">
     Next Steps in Advancing Neural Solvers
    </h3>
    <p class="notion-paragraph" id="1cf77d2a-033f-4a58-a62f-a67e86084517">
     In the upcoming posts, we will explore innovative neural solver methodologies that promise greater fidelity and robustness to address the challenges shown in vanilla DL approach.
    </p>
    <p class="notion-paragraph" id="55154c7c-bee4-4a0c-9427-8257b825f346">
     Specifically, three advanced methods will be introduced:
     <strong>
      Neural Ordinary Differential Equations (NeuralODE)
     </strong>
     ,
     <strong>
      Physics-Informed Neural Networks (PINN)
     </strong>
     , and
     <strong>
      Neural Operators
     </strong>
     .
    </p>
    <ul class="bulleted-list" id="f406a2c5-6e64-41e7-a81e-fbf1943d9fdb">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       NeuralODE
      </strong>
      represents a fusion of deep learning with differential equations, offering a continuous-time model that can dynamically adapt to the underlying physics.
     </li>
    </ul>
    <ul class="bulleted-list" id="1d6ff0e8-ea35-45f7-8a50-a0982766b110">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       PINN
      </strong>
      incorporates physical laws into the neural network’s loss function, enhancing prediction accuracy and model generalizability.
     </li>
    </ul>
    <ul class="bulleted-list" id="1fee81d1-3458-4dac-9d53-ace26994ccdc">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Neural Operators
      </strong>
      learn mappings in infinite-dimensional spaces, enabling broad applicability across different geometries and conditions.
     </li>
    </ul>
    <p class="notion-paragraph" id="0314080c-9f52-43ce-9ef9-043192394e20">
     These methods not only address traditional DL model limitations but also offer new ways to simulate complex physical systems with high precision. The upcoming post will delve into a detailed examination of each technique, their potential, and their applications.
    </p>
    <p class="notion-paragraph" id="a176787e-a1c4-4ee2-9b5f-652ea1760fa0">
    </p>
   </div>
  </article>
  <span class="sans" style="font-size:14px;padding-top:2em">
  </span>
 </body>
</html>
