---
layout: page
permalink: pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content.html
---
<html>
 <head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
  <title>
   Neural Solver Towards Future of Simulation: Deep Dive
  </title>
 </head>
 <body>
  <article class="page sans" id="2e51a3db-a7ec-4f93-83bf-8da9826b472f">
   <header>
    <h1 class="page-title" style="font-weight: 650; margin-top: 0px;">
     Neural Solver Towards Future of Simulation: Deep Dive
    </h1>
    <h6 style="font-weight: 300; font-size: 0.9em;">
     Date: 08-09-2024 | Author: Ki-Ung Song
    </h6>
    <p class="notion-paragraph">
    </p>
   </header>
   <div class="page-body">
    <details style="padding-bottom: 10px;">
     <summary>
      <span style="font-weight: 650;">
       Neural Solver Towards Future of Simulation Series
      </span>
     </summary>
     <ul class="bulleted-list">
      <li class="notion-li">
       <div>
        <u>
         Neural Solver Towards Future of Simulation: Deep Dive - Current Post
        </u>
       </div>
      </li>
      <li class="notion-li">
       <a href="{{ site.baseurl }}{% link pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content.html %}">
        <div>
         Neural Solver Towards Future of Simulation: Exploration
        </div>
       </a>
      </li>
      <li class="notion-li">
       <a href="{{ site.baseurl }}{% link pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content.html %}">
        <div>
         Neural Solver Towards Future of Simulation: Intro
        </div>
       </a>
      </li>
     </ul>
    </details>
    <nav class="table_of_contents" id="04436bf1-2799-4c22-bf7e-0d62f47eeefd">
     <details>
      <summary>
       <span style="font-weight: 650; white-space: nowrap;">
        Table of Contents
       </span>
      </summary>
      <div style="padding-top: 10px; padding-bottom: 10px;">
       <div class="table_of_contents-item table_of_contents-indent-0">
        <a class="table_of_contents-link" href="#e55cea30-c1c8-4f73-b7b9-c238d1e0e2bc">
         Neural Operator
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#342e0d53-6f77-40eb-99bf-51c865f222a0">
         Operator Learning Intro: DeepONet
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#db3ef523-feb4-401b-98c0-b53a5c06008e">
         Motivation
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#e5148759-59db-4b35-8f64-ac820f6fbc0d">
         Neural Operator Design
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#303dbd49-d535-48f8-a21a-81b4e11bf558">
         Main Layer Design
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#2dca303f-4d46-457a-8db6-934b64dcb8f6">
         Theoretical Consideration
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-0">
        <a class="table_of_contents-link" href="#049d0c37-742b-45ac-9605-7faa8301f1b2">
         Application of Neural Solvers
        </a>
       </div>
      </div>
     </details>
    </nav>
    <p class="notion-paragraph" id="7eebd841-15f4-494f-911d-77b02cae990c">
     In previous post, we explored Neural ODEs and PINNs approaches for neural solvers.
    </p>
    <p class="notion-paragraph" id="7be7b25d-1c0d-4896-b77c-fa35182dbc97">
     In this post, we examine Neural Operator, a method that opens up new possibilities for efficient and accurate simulations.
    </p>
    <p class="notion-paragraph" id="78641c6e-a93a-49ef-bd28-ba85c0882679">
     Additionally, practical applications of these previously introduced neural solvers will be presented to showcase their real-world potential.
    </p>
    <hr id="42e4aceb-580c-4f23-adf3-803ec6ac9bef"/>
    <h2 class="" id="e55cea30-c1c8-4f73-b7b9-c238d1e0e2bc" style="font-weight: 650;">
     Neural Operator
    </h2>
    <p class="notion-paragraph" id="76bcdc57-bfde-41eb-87c0-58ccfc5577e8">
     Neural Operators (NOs) leverage the power of DL to model the underlying functions and mappings directly. This approach is called operator learning.
    </p>
    <div class="notion_callout">
     <div style="font-size:1.5em">
      <span class="icon">
       💡
      </span>
     </div>
     <div class="text" style="width:100%">
      <strong>
       Operator Learning: Learning operators that are mappings between infinite-dimensional function spaces.
      </strong>
     </div>
    </div>
    <h3 class="" id="342e0d53-6f77-40eb-99bf-51c865f222a0" style="font-weight: 650;">
     Operator Learning Intro: DeepONet
    </h3>
    <p class="notion-paragraph" id="98031dd6-95bf-41aa-ba0e-de62ba9faf47">
     The term “operator learning” may not be clear at first glance. We will illustrate this concept with the example of DeepONet, one of the milestone achievements in operator learning.
    </p>
    <ul class="bulleted-list" id="6b89db76-8d73-4c1f-81e0-6d5f865f526e">
     <li class="notion-li" style="list-style-type:disc">
      Original paper:
      <a href="https://arxiv.org/abs/1910.03193">
       DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org)
      </a>
     </li>
    </ul>
    <ul class="bulleted-list" id="a349824e-cfdf-4a90-aa71-4c41e164baf9">
     <li class="notion-li" style="list-style-type:disc">
      Good examples and illustrations from
      <a href="https://lululxvi.github.io/files/talks/2020SIAMMDS_MS1.pdf">
       DeepONet: Learning nonlinear operators (lululxvi.github.io)
      </a>
      will be introduced here.
     </li>
    </ul>
    <p class="notion-paragraph" id="8ff74e3e-44a8-4a6f-8c62-b8fcb8aeac03">
     <strong>
      Function vs. Operator
     </strong>
    </p>
    <ul class="bulleted-list" id="f0cb584a-8f9e-48b8-bca3-bd71f8a7a1a4">
     <li class="notion-li" style="list-style-type:disc">
      Function:
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \R^{d_1} → \R^{d_2}
       </span>
      </span>
      : Below is a function for
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \R^{28 \times 28} \rightarrow \R^{10}
       </span>
      </span>
      .
      <figure class="image" id="b1e9300e-2783-4105-b9ea-8e129486779e">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_9.49.53_AM.png" style="width:384px"/>
       <figcaption>
        <a href="https://lululxvi.github.io/files/talks/2020SIAMMDS_MS1.pdf">
         DeepONet: Learning nonlinear operators (lululxvi.github.io)
        </a>
       </figcaption>
      </figure>
     </li>
    </ul>
    <ul class="bulleted-list" id="99efb01a-7337-45b7-b812-8037fdb1f110">
     <li class="notion-li" style="list-style-type:disc">
      Operator:
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \text{function} (∞-\dim) \mapsto \text{function} (∞-\dim)
       </span>
      </span>
      <ul class="bulleted-list" id="9f655874-ed37-46d8-96cb-3754ac35ee17">
       <li class="notion-li" style="list-style-type:circle">
        Derivative (local):
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          x(t) \mapsto x'(t)
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="33c0191f-6376-4573-9e5f-a8b20205983a">
       <li class="notion-li" style="list-style-type:circle">
        Integral (global):
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          x(t) \mapsto \int K(s, t)x(s)ds
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="b4b62acd-1fac-4a7d-bbaf-e750723083dd">
       <li class="notion-li" style="list-style-type:circle">
        Dynamic system: Differential equation
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="71cc7119-e533-42da-bb88-ed28de3fb67a">
     <strong>
      Problem Setup
     </strong>
    </p>
    <p class="notion-paragraph" id="5fb9dcbb-0105-4034-a481-e62656a57adf">
     The goal is to learn an operator
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G
      </span>
     </span>
     that maps a function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u
      </span>
     </span>
     to another function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G(u)
      </span>
     </span>
     . Formally:
    </p>
    <figure class="equation" id="c14e9691-6807-4bac-8355-4ebf65ca6965">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        G : u \mapsto G(u), \\
G(u) : y ∈ \R^d \mapsto G(u)(y) ∈ \R
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="e15b513d-fe0b-4871-a886-d18734b1a0e3">
     More concretely, given a differential equation of form:
    </p>
    <figure class="equation" id="635916ca-c415-4938-a317-591a12539472">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal L s = u
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="d5c1afeb-742b-4c3f-872a-0e1a508d4655">
     We want operator
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G:u \mapsto s
      </span>
     </span>
     .
    </p>
    <ul class="bulleted-list" id="4817bde9-bfdb-47f2-b2dd-6a1659fd5fa4">
     <li class="notion-li" style="list-style-type:disc">
      Inputs:
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u
       </span>
      </span>
      at sensors
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \{x_1, x_2, \cdots, x_m\}
       </span>
      </span>
      and
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        y ∈ \R^d
       </span>
      </span>
      .
      <ul class="bulleted-list" id="4c3bba50-4d38-409a-9a97-405b1531beca">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         What exactly input
        </strong>
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          x
         </span>
        </span>
        <strong>
         and
        </strong>
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          y
         </span>
        </span>
        <strong>
         look like? It depends on the given DE.
        </strong>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="991153ab-d409-4c42-b30f-40cf2a3553cd">
     <li class="notion-li" style="list-style-type:disc">
      Output:
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        G(u)(y) ∈ \R
       </span>
      </span>
      .
      <figure class="image" id="d79c6961-315f-440d-bedc-02a24e1da49a" style="text-align:center">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_10.36.31_AM.png" style="width:432px"/>
       <figcaption>
        <a href="https://lululxvi.github.io/files/talks/2020SIAMMDS_MS1.pdf">
         DeepONet: Learning nonlinear operators (lululxvi.github.io)
        </a>
       </figcaption>
      </figure>
     </li>
    </ul>
    <p class="notion-paragraph" id="b034327d-8eef-4a91-af97-4d457c852feb">
     <strong>
      Architecture
     </strong>
    </p>
    <p class="notion-paragraph" id="afd0c471-1afe-42b3-9a7f-a5faa1aa5e8e">
     DeepONet consists of
     <strong>
      two encoder
     </strong>
     networks:
    </p>
    <ol class="numbered-list" id="0e15b23b-30ef-43fd-b1ce-b0a5fcfc88c3" start="1" type="1">
     <li class="notion-li">
      <strong>
       Branch Net
      </strong>
      : Encodes the input function into a latent representation. i.e. this encoder is used for encoding the discretized input function.
     </li>
    </ol>
    <ol class="numbered-list" id="8f86ce49-342e-40c9-8065-20e95513f5fb" start="2" type="1">
     <li class="notion-li">
      <strong>
       Trunk Net
      </strong>
      : Encodes the coordinates or specific points where the output is evaluated. i.e. the second encoder is used for encoding the location of the output functions.
      <figure class="image" id="cc014401-67d8-43f8-b423-2d124f721bcf">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_10.23.23_AM.png" style="width:589px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/1910.03193">
         DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org)
        </a>
       </figcaption>
      </figure>
     </li>
    </ol>
    <p class="notion-paragraph" id="bd10ba1b-eed4-4528-88a6-2854df3d7ec5">
     The goal is to approximate the target operator
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G
      </span>
     </span>
     with a NN
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G_{\theta}
      </span>
     </span>
     :
    </p>
    <figure class="equation" id="1ed01d04-0930-455d-95d0-00e1c6c04592">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        G(u)(y) ≈ G_{\theta}(u)(y) = \sum^p_{k=1} b_k(u) · t_k(y)
       </span>
      </span>
     </div>
    </figure>
    <ul class="bulleted-list" id="8117b756-42b6-423d-9636-6eee806a3126">
     <li class="notion-li" style="list-style-type:disc">
      Prior knowledge:
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u
       </span>
      </span>
      and
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        y
       </span>
      </span>
      are independent.
     </li>
    </ul>
    <ul class="bulleted-list" id="f412cb5e-f983-4df2-8576-cbebbdcd7083">
     <li class="notion-li" style="list-style-type:disc">
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        G(u)(y)
       </span>
      </span>
      is a function of
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        y
       </span>
      </span>
      conditioning on
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u
       </span>
      </span>
      .
      <ul class="bulleted-list" id="bb2b3890-2961-4736-83e2-6216236cb672">
       <li class="notion-li" style="list-style-type:circle">
        Branch net:
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          b_k(u)
         </span>
        </span>
        ,
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          u
         </span>
        </span>
        -dependent coefficients.
       </li>
      </ul>
      <ul class="bulleted-list" id="2575de49-8549-45c7-8571-a8a7223756f1">
       <li class="notion-li" style="list-style-type:circle">
        Trunk net:
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          t_k(y)
         </span>
        </span>
        , basis functions of
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          y
         </span>
        </span>
        .
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="a07fc63a-a0d4-4600-857d-8f4f217fe547">
     <strong>
      Example Case: Diffusion-reaction system
     </strong>
    </p>
    <p class="notion-paragraph" id="24d86976-2be5-451c-9225-e0f424cb589c">
     Given a PDE:
    </p>
    <figure class="image" id="88dbc42b-e0cf-4575-b067-bebc17109960" style="text-align:center">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_10.55.35_AM.png" style="width:432px"/>
     <figcaption>
      <a href="https://lululxvi.github.io/files/talks/2020SIAMMDS_MS1.pdf">
       DeepONet: Learning nonlinear operators (lululxvi.github.io)
      </a>
     </figcaption>
    </figure>
    <p class="notion-paragraph" id="e6303a6b-20be-4af1-b73a-27737400119d">
     Input for branch net becomes
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u(x_i)
      </span>
     </span>
     and input for trunk net becomes
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       (x_i,t_i)
      </span>
     </span>
     .
     <div class="indented">
      <figure class="image" id="83bcff24-6965-4294-9f0c-3d9681d4fcf0">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_11.15.09_AM.png" style="width:1442px"/>
       <figcaption>
        <a href="https://lululxvi.github.io/files/talks/2020SIAMMDS_MS1.pdf">
         DeepONet: Learning nonlinear operators (lululxvi.github.io)
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="e33a63eb-3377-4a73-ac44-059e21967ec9">
       <li class="notion-li" style="list-style-type:disc">
        For a fixed function
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          u:\R \rightarrow \R
         </span>
        </span>
        ,
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          P
         </span>
        </span>
        points are sampled for each
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          (x_i,t_i)
         </span>
        </span>
        with input function estimation
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          u(x_i)
         </span>
        </span>
        and solution function
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          s(x_t,t_i)
         </span>
        </span>
        obtained via simulation:
        <figure class="equation" id="97b25e75-0134-401d-8e02-157ea7568f00">
         <div class="equation-container">
          <span class="katex-display">
           <span class="equation_math_block">
            \left((x_i,t_i), u(x_i), s(x_i,t_i)\right)
           </span>
          </span>
         </div>
        </figure>
       </li>
      </ul>
      <ul class="bulleted-list" id="d5ce831a-7e4f-476f-9fdf-46e3fca18c8a">
       <li class="notion-li" style="list-style-type:disc">
        Number of total dataset:
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \text{number of function }u \times P
         </span>
        </span>
        .
       </li>
      </ul>
     </div>
    </p>
    <p class="notion-paragraph" id="4a28b403-8d39-4450-a721-197d6ecedec0">
     The NN output approximates as:
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G_\theta(u)(x_i,t_i) \approx s(x_i,t_i)
      </span>
     </span>
     .
    </p>
    <ul class="bulleted-list" id="55f72a21-0a44-4e51-8d9a-5d6669c5211b">
     <li class="notion-li" style="list-style-type:disc">
      A naive data-driven supervised loss can be used.
     </li>
    </ul>
    <ul class="bulleted-list" id="15c98a9d-d324-455d-af92-2341fa725a1a">
     <li class="notion-li" style="list-style-type:disc">
      A physics-informed loss can also be used.
     </li>
    </ul>
    <p class="notion-paragraph" id="aa7609c5-a988-444e-b22b-66f84819519a">
     <strong>
      Discussion
     </strong>
    </p>
    <ul class="bulleted-list" id="82bfebe7-1779-4318-9721-f0fa08a74974">
     <li class="notion-li" style="list-style-type:disc">
      DeepONet introduces a theorem ensuring it is a universal approximator for operator learning. This theorem underpins DeepONet's ability to accurately learn and generalize mappings between infinite-dimensional spaces.
      <figure class="image" id="274c56a1-df88-4dc6-bbe7-ed86d9819915">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_12.51.32_AM.png" style="width:1930px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/1910.03193">
         DeepONet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators (arxiv.org)
        </a>
       </figcaption>
      </figure>
     </li>
    </ul>
    <ul class="bulleted-list" id="f883e89e-2c50-4283-ac6f-3e462c6a9cde">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Pros
      </strong>
      <ul class="bulleted-list" id="2442426e-68d4-4166-af6d-d718a163ad5e">
       <li class="notion-li" style="list-style-type:circle">
        Accurately approximates mappings between infinite-dimensional spaces.
       </li>
      </ul>
      <ul class="bulleted-list" id="df4ceab7-3643-4f8a-9f84-7865044f6e58">
       <li class="notion-li" style="list-style-type:circle">
        Efficient for real-time simulations once trained.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="7629675d-e278-4cca-8ed1-8aeec53c929e">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Cons
      </strong>
      <ul class="bulleted-list" id="79db9660-9b18-4eeb-b8b8-edb98f845cd3">
       <li class="notion-li" style="list-style-type:circle">
        Requires a large amount of training data.
       </li>
      </ul>
      <ul class="bulleted-list" id="c2870156-5522-48a3-aa5f-93c1631cdeb6">
       <li class="notion-li" style="list-style-type:circle">
        Not resolution invariant; only takes input functions at a fixed discretization.
       </li>
      </ul>
     </li>
    </ul>
    <h3 class="" id="db3ef523-feb4-401b-98c0-b53a5c06008e" style="font-weight: 650;">
     Motivation
    </h3>
    <p class="notion-paragraph" id="18e82c13-2add-4f93-8e07-50d58f123799">
     Now, let's dive into an important milestone for neural solvers in operator learning: Neural Operators. The DeepONet from last section is also considered as a neural operator in nowadays.
    </p>
    <p class="notion-paragraph" id="66ef0aae-1aed-478f-998a-93f4f1d1e9f3">
     However, we'll discuss it in the context of the original Neural Operator paper:
     <a href="https://arxiv.org/abs/2108.08481">
      Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
     </a>
     . In this context, the following figure highlights that DeepONet lacks discretization invariance:
    </p>
    <figure class="image" id="cf10e9cf-86db-4499-906d-1ec8e4d3135f">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Untitled.png" style="width:1762px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2108.08481">
       Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="2465ebfa-13b1-4d2d-b9d2-0786b65ca232">
     <li class="notion-li" style="list-style-type:disc">
      What is discretization invariance and why it is important?
     </li>
    </ul>
    <p class="notion-paragraph" id="7d2cac5d-5bec-478e-8b43-c8c4aa6527ab">
     <strong>
      Resolution of Simulated Data
     </strong>
    </p>
    <figure class="image" id="f105ed3f-8525-461c-beff-d63f1ef37b30">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_8.15.20_PM.png" style="width:576px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2108.08481">
       Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="fc2d2032-3317-4245-95ab-b286574dbd60">
     <li class="notion-li" style="list-style-type:disc">
      The figure displays simulation data with increasing mesh density from left to right: sparse (low resolution), medium (medium resolution), and dense (high resolution).
     </li>
    </ul>
    <ul class="bulleted-list" id="baa2e95d-bbb0-41d4-9925-75bcec7e93e2">
     <li class="notion-li" style="list-style-type:disc">
      Higher resolution provides better quality simulations but requires more computational resources. Training models on low-resolution data and inferring on high-resolution data is desirable but can lead to performance degradation due to out-of-distribution issues.
     </li>
    </ul>
    <ul class="bulleted-list" id="fd577a10-4aef-44a8-9de7-447f7aba497d">
     <li class="notion-li" style="list-style-type:disc">
      Discretization invariance allows models to handle different resolutions effectively, ensuring models trained on low-resolution data generalize well to high-resolution settings, avoiding the out-of-distribution issues that degrade performance.
     </li>
    </ul>
    <p class="notion-paragraph" id="edc44624-f722-4cdf-8f86-928cea2a2582">
     <strong>
      Framework
     </strong>
    </p>
    <p class="notion-paragraph" id="8c91d0fb-5c64-4d2f-af55-a909fb4a0154">
     Consider the generic family of PDEs:
    </p>
    <figure class="equation" id="94630941-28b7-4b4e-b9f9-5c1cd18346e4">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        (\mathcal{L}_au)(x) = f(x), \quad  x \in D\\ 
u(x) = 0,\quad x ∈ ∂D.
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="03285aac-4a6c-4baa-bec1-2d7cede772a3">
     where
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       D ⊂ \R^d
      </span>
     </span>
     is spatial domain for the PDE with points
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       x ∈ D
      </span>
     </span>
     in the the spatial domain.
    </p>
    <ul class="bulleted-list" id="49948300-8553-4b83-a467-8771bb66b5dd">
     <li class="notion-li" style="list-style-type:disc">
      e.g. Standard second order elliptic PDE
      <figure class="equation" id="88c20478-107d-432f-9066-3efb50d45467">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          - \nabla \cdot (a(x) \nabla u(x))  = f(x), \quad  x \in D \\
u(x) = 0, \quad x \in \partial D
         </span>
        </span>
       </div>
      </figure>
     </li>
    </ul>
    <p class="notion-paragraph" id="a88decb3-d0ad-4536-94b8-3923aa3202a9">
     Input function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a:D\rightarrow \R^{d_a}
      </span>
     </span>
     is defined with bounded domain
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       D
      </span>
     </span>
     and
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a ∈ \mathcal A
      </span>
     </span>
     .
    </p>
    <ul class="bulleted-list" id="2a63f003-bb6d-487f-8401-48da8f5668df">
     <li class="notion-li" style="list-style-type:disc">
      It includes coefficients, boundaries, and/or initial conditions.
     </li>
    </ul>
    <ul class="bulleted-list" id="e4165842-3682-4890-8cb1-37dc82c76adb">
     <li class="notion-li" style="list-style-type:disc">
      More concretely, initial state
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u_0
       </span>
      </span>
      can be an input function.
     </li>
    </ul>
    <p class="notion-paragraph" id="c7b5d59e-0152-4df6-826e-0b96aaa28e25">
     Output function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u : D' → \R^{d_u}
      </span>
     </span>
     is defined with bounded domain
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       D'\sub \R^{d'}
      </span>
     </span>
     and
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u \in \mathcal U
      </span>
     </span>
     :
     <strong>
      target solution functions
     </strong>
     .
    </p>
    <ul class="bulleted-list" id="c9095540-8de5-44f3-ad68-7e40fc97220d">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       More concretely, solution
      </strong>
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u_t
       </span>
      </span>
      <strong>
       at some desired timestep
      </strong>
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        t
       </span>
      </span>
      <strong>
       can be target solution function.
      </strong>
      <figure class="image" id="b8d24b4e-c53e-4000-aca5-b3d8c98340b0" style="text-align:center">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_11.35.29_PM.png" style="width:624px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/2108.08481">
         Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
        </a>
       </figcaption>
      </figure>
     </li>
    </ul>
    <ul class="bulleted-list" id="1b9eb81c-b008-4223-b57c-c25612ce91a4">
     <li class="notion-li" style="list-style-type:disc">
      We can simply assume
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        D=D'
       </span>
      </span>
      .
     </li>
    </ul>
    <p class="notion-paragraph" id="3e59e0f7-4670-4255-b29c-4cfcf2d95da4">
     Optimal operator for this PDE can be defined as
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal{G} = \mathcal{L}_a^{-1}f:\mathcal A \rightarrow \mathcal U : a \mapsto u
      </span>
     </span>
     . i.e. we aim to find the solution at the target timestep given initial conditions.
    </p>
    <p class="notion-paragraph" id="f1ef4c0c-21b0-4a99-9aa6-ec8b5a046fb2">
     <strong>
      Theoretical Consideration
     </strong>
    </p>
    <p class="notion-paragraph" id="09a2a7f0-5b7c-4f24-bef9-8fdf4d00bae8">
     Given a linear operator
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal{L}_a
      </span>
     </span>
     , there exists a unique function called Green’s function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G_a : D \times D \to \mathbb{R}
      </span>
     </span>
     s.t.
    </p>
    <figure class="equation" id="d5f70ae1-a991-4adf-a99d-bd2e4c2fc3dd">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal{L}_a G_a(x, \cdot) = \delta_x
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="4b347af5-72cf-44fa-9272-d7069a93f6e1">
     where
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \delta_x
      </span>
     </span>
     is the delta measure on
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \R^d
      </span>
     </span>
     centered at
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       x
      </span>
     </span>
     .
    </p>
    <ul class="bulleted-list" id="1c9648d5-e5f8-4521-9c3c-61ade6e76289">
     <li class="notion-li" style="list-style-type:disc">
      Note that
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        a
       </span>
      </span>
      is denoted on
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        G_a
       </span>
      </span>
      since it is dependent on
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \mathcal L_a
       </span>
      </span>
      .
     </li>
    </ul>
    <p class="notion-paragraph" id="bbeec932-542a-4894-85c6-23b84f7cec50">
     Then true operator
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal{G}
      </span>
     </span>
     can be written as:
    </p>
    <figure class="equation" id="735b4d22-3e8c-47a9-b0d4-56a4c7b0939c">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal{G}: a \mapsto u(x) = \int_D G_a(x,y)f(y) \: dy
       </span>
      </span>
     </div>
    </figure>
    <ul class="bulleted-list" id="5a8cff4a-d7af-4837-b28b-41259743858c">
     <li class="notion-li" style="list-style-type:disc">
      Since
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \mathcal{L}_a
       </span>
      </span>
      is linear operator with respect to variable
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        x
       </span>
      </span>
      , computation order with
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \int_D \cdot \, dy
       </span>
      </span>
      can be exchanged:
      <figure class="equation" id="d87b47ad-258b-4efd-ae56-cbc764641e00">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          f(x)= \int_D \delta_x(y) f(y) dy = \int_D \mathcal{L}_a G_a(x,y)f(y)dy \\
= \mathcal{L}_a \left(\int_D G_a(x,y)f(y)dy\right)
         </span>
        </span>
       </div>
      </figure>
     </li>
    </ul>
    <p class="notion-paragraph" id="79f809e2-0365-40b5-b79e-206dd920e8ff">
     With fixed
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       f
      </span>
     </span>
     , we can readily check that solution
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u
      </span>
     </span>
     is only dependent on input function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a
      </span>
     </span>
     .
    </p>
    <p class="notion-paragraph" id="44f11ea7-f168-4e43-ad64-2620a0c477eb">
     <strong>
      How to find such Green’s function?
     </strong>
    </p>
    <p class="notion-paragraph" id="3202a866-0eae-4666-8bbe-f9dd7c687736">
     If the operator
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal{L}_a
      </span>
     </span>
     admits a complete set of eigenfunctions (function version of eigenvector)
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \Psi_i(x)
      </span>
     </span>
     , i.e.
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal{L}_a\Psi_i=\lambda_i \Psi_i
      </span>
     </span>
     :
    </p>
    <ul class="bulleted-list" id="827ba10c-9a40-42a2-86c5-0079b288decc">
     <li class="notion-li" style="list-style-type:disc">
      Completeness means
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \delta(x-y)=\sum_i \Psi_i(x)^*\Psi_i(y)
       </span>
      </span>
      holds, where
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        *
       </span>
      </span>
      denotes complex conjugation.
     </li>
    </ul>
    <ul class="bulleted-list" id="d399191a-4913-4c6f-aa25-0595524d3506">
     <li class="notion-li" style="list-style-type:disc">
      Then, the Green’s function is given by:
      <figure class="equation" id="b09a5a83-cf06-426b-aaaf-506f749b348a">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          G_a(x,y)=\sum_i \dfrac{\Psi_i(x)^*\Psi_i(y)}{\lambda_i}
         </span>
        </span>
       </div>
      </figure>
     </li>
    </ul>
    <p class="notion-paragraph" id="3b16526d-ea18-4d14-b0c3-7826ab980e3f">
     Analogous to the kernel trick in ML,
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       G_a(x,y)
      </span>
     </span>
     <strong>
      can be viewed as a kernel
     </strong>
     with feature function:
    </p>
    <figure class="equation" id="5aa4b8cc-1070-4e10-b5d6-ce8c97748ce1">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \phi(y)=[\Psi_1(y) / \sqrt{\lambda_1},\Psi_2(y) / \sqrt{\lambda_2}, \cdots]
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="7e76f4ed-3092-4e44-94cd-b2276545a50d">
     <strong>
      This perspective of Green's function will inform the design of the neural operator layer, serving as an inductive bias, which will be introduced later.
     </strong>
    </p>
    <h3 class="" id="e5148759-59db-4b35-8f64-ac820f6fbc0d" style="font-weight: 650;">
     Neural Operator Design
    </h3>
    <p class="notion-paragraph" id="256bbecb-c133-436d-b656-1988cb18a204">
     <strong>
      Main goal of neural operator is to construct NN
     </strong>
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal G_{\theta}
      </span>
     </span>
     <strong>
      to approximates
     </strong>
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal G
      </span>
     </span>
     <strong>
      .
     </strong>
    </p>
    <ul class="bulleted-list" id="b7c088bb-d485-4271-bd42-9c8e39b707e2">
     <li class="notion-li" style="list-style-type:disc">
      With discretization invariance and operator learning framework.
     </li>
    </ul>
    <ul class="bulleted-list" id="e59aa661-4a2b-48b1-b4b3-9fd85debddc1">
     <li class="notion-li" style="list-style-type:disc">
      Without needing knowledge of the underlying PDE.
     </li>
    </ul>
    <p class="notion-paragraph" id="296b88cd-432e-4dc8-835e-9e9b55d7940a">
     <strong>
      Loss objective
     </strong>
    </p>
    <p class="notion-paragraph" id="841e0b2d-5f8b-42d5-af87-30087ed190ca">
     Given the dataset
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \{a_i, u_i\}^N_{i=1}
      </span>
     </span>
     (observations), simple supervised loss is used:
    </p>
    <figure class="equation" id="63ffcbca-8318-4f2d-9fe2-7acad6e6e7fc">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \min_θ\dfrac{1}{N}\sum^N_{i=1} \Vert u_i − \mathcal G_θ(a_i) \Vert^2_\mathcal U
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="1315d8fc-fe43-485b-af43-4b689d00fc76">
     <strong>
      Architecture
     </strong>
    </p>
    <p class="notion-paragraph" id="d9a67ece-47b1-430e-a336-875520d0200a">
     The original Neural Operator (NO) architecture has three main components:
    </p>
    <figure class="equation" id="1b6d1793-c9c8-4a37-84ba-5dd39e2a0bee">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal G_θ(a) = (Q ◦ W_L ◦ · · · ◦ W_1 ◦ P)(a)
       </span>
      </span>
     </div>
    </figure>
    <figure class="image" id="aeb44da6-5882-4b2b-9341-533f53305afc">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_8.26.17_PM.png" style="width:576px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2108.08481">
       Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="f2df3174-89d2-4f86-9980-6acc5f83083b">
     <li class="notion-li" style="list-style-type:disc">
      Lifting mapping
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        P
       </span>
      </span>
      :
      <ul class="bulleted-list" id="8f93397e-961c-4494-9212-c20221dd1689">
       <li class="notion-li" style="list-style-type:circle">
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          P : \R^{d_a} → \R^{d_{v_0}}
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="e933855d-9ba7-4d6c-9fec-599045f5a988">
       <li class="notion-li" style="list-style-type:circle">
        To take advantage of NN, it sends input
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          a_i
         </span>
        </span>
        to a high dimensional representation space.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="5480059b-eed3-431c-a750-63b7bbde765e">
     <li class="notion-li" style="list-style-type:disc">
      Main Layer
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        W_i
       </span>
      </span>
      :
      <ul class="bulleted-list" id="a74ede05-7bd6-4c84-85d5-26a4fff5b3c3">
       <li class="notion-li" style="list-style-type:circle">
        It is designed for “Iterative Kernel Integration” scheme.
       </li>
      </ul>
      <ul class="bulleted-list" id="30448065-d8db-487d-8bea-54c0bf8edfe4">
       <li class="notion-li" style="list-style-type:circle">
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          W_i: \R^{d_{v_i}} → \R^{d_{v_{i+1}}}
         </span>
        </span>
        is local linear operators.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="c7d3009e-bcce-4ec2-9b07-3f3c9beafcba">
     <li class="notion-li" style="list-style-type:disc">
      Projection mapping
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        Q
       </span>
      </span>
      :
      <ul class="bulleted-list" id="8bd7d70d-f6d5-4f34-82c4-9936af7a5d4f">
       <li class="notion-li" style="list-style-type:circle">
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          Q : \R^{d_{v_T}} → \R^{d_u}
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="45f448d6-390a-4219-91be-da1c59009774">
       <li class="notion-li" style="list-style-type:circle">
        Projects the high-dimensional representation back, obtaining the final solution
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          u
         </span>
        </span>
        .
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="062b9ae5-87c3-4f9d-886f-31307279b738">
     The lifting and projection mappings are straightforward. The main layer's design, based on the inductive bias of Green’s function, will be discussed in the next section.
    </p>
    <h3 class="" id="303dbd49-d535-48f8-a21a-81b4e11bf558" style="font-weight: 650;">
     Main Layer Design
    </h3>
    <p class="notion-paragraph" id="07563347-44cf-4857-867b-941474d4f5c3">
     <strong>
      Integral Kernel Operators
     </strong>
    </p>
    <p class="notion-paragraph" id="829e157c-4bc2-4a68-bea7-4064328457e6">
     As discussed earlier, since Green's function can be viewed as a kernel, we can modify the solution
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u(x) = \int_D G_a(x,y)f(y) \: dy
      </span>
     </span>
     by introducing a kernel network
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \kappa_\phi
      </span>
     </span>
     :
    </p>
    <figure class="equation" id="06d9354d-d492-40fe-a798-d25d2c4c75bb">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        u(x) = \int_D \kappa_\phi(x,y,a(x),a(y))f(y) \: dy
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="ab1ad0f5-346e-4342-be7e-1f53233fe52f">
     Based on this, to train the NO without knowing
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       f
      </span>
     </span>
     , we design an iterative update scheme:
    </p>
    <figure class="equation" id="ae2fd07d-e5fc-4f8c-b983-bab1e56ed557">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        v_{t+1}(x) = \sigma\Big( W v_t(x) + \int_{B(x,r)} \kappa_{\phi}\big(x,y,a(x),a(y)\big) v_t(y)\: dy \Big) \tag{1}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="ae88343c-9890-4413-aed9-cbed02870a40">
     where
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       t=1,\ldots,N-1
      </span>
     </span>
     holds.
    </p>
    <ul class="bulleted-list" id="4765f6f0-b846-4f37-a0b2-84663e7baae6">
     <li class="notion-li" style="list-style-type:disc">
      Here,
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        v_t(x)
       </span>
      </span>
      and
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        v_{t+1}(x)
       </span>
      </span>
      are input &amp; output representation respectively.
     </li>
    </ul>
    <ul class="bulleted-list" id="2a71e7fe-fd68-40be-baa6-50290343dc89">
     <li class="notion-li" style="list-style-type:disc">
      This iterative scheme functions as a layer of the neural operator.
     </li>
    </ul>
    <div class="notion_callout">
     <div style="font-size:1.5em">
      <span class="icon">
       💡
      </span>
     </div>
     <div class="text" style="width:100%">
      <strong>
       Until this stage, the iterative update scheme was designed with Green’s function as an inductive bias for neural operator layers.
      </strong>
     </div>
    </div>
    <p class="notion-paragraph" id="7d0e51ef-6352-4f8f-86b5-70185da4b581">
     The detailed design of
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \int \kappa_{\phi} v_t
      </span>
     </span>
     in equation (1) leads to various NO layer versions, resulting in different NO variants. This section will introduce the FNO, as proposed in the original paper.
    </p>
    <p class="notion-paragraph" id="fd1b4271-90d0-4d62-a7ed-b283b3a36946">
     <strong>
      FNO (Fourier Neural Operator)
     </strong>
    </p>
    <ul class="bulleted-list" id="0a596413-6ef0-4944-ad1c-090675a10999">
     <li class="notion-li" style="list-style-type:disc">
      Let’s follow the expression of the original paper:
      <figure class="image" id="0965f75a-3f9e-4b93-ae46-9ee10d72df9d" style="text-align:center">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_2.26.25_PM.png" style="width:624px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/2108.08481">
         Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
        </a>
       </figcaption>
      </figure>
      <figure class="image" id="a497c275-20e2-4d46-b819-1c6b2321c163">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_2.26.48_PM.png" style="width:2002px"/>
      </figure>
     </li>
    </ul>
    <ul class="bulleted-list" id="2f43e328-0583-4b7d-9807-51091eb63441">
     <li class="notion-li" style="list-style-type:disc">
      Convolution Theorem
      <ul class="bulleted-list" id="b6885ac1-b6e2-46fa-a7e2-838ee01159cd">
       <li class="notion-li" style="list-style-type:circle">
        Recall the definition of convolution:
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          f*g=\int f(x)g(y-x)dx
         </span>
        </span>
        .
        <ul class="bulleted-list" id="88afea3c-7a3e-4e83-9ffb-835b3c86609c">
         <li class="notion-li" style="list-style-type:square">
          The kernel integral operator can be seen as a convolution operator:
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            \mathcal K(\phi)v_t \approx K(\phi) * v_t
           </span>
          </span>
          .
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="0a675f61-2f6a-4277-8ed0-889281692aeb">
       <li class="notion-li" style="list-style-type:circle">
        Convolution transforms to multiplication under Fourier Transform (FT):
        <ul class="bulleted-list" id="2439d0dc-eecf-421d-9fe5-436825743f5c">
         <li class="notion-li" style="list-style-type:square">
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            \mathcal{F}(f *g) = \mathcal{F}(f)\mathcal{F}(g)
           </span>
          </span>
         </li>
        </ul>
        <ul class="bulleted-list" id="254099b3-51f2-442b-9106-5f76aba31940">
         <li class="notion-li" style="list-style-type:square">
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            f *g = \mathcal{F}^{-1}(\mathcal{F}(f)\mathcal{F}(g))
           </span>
          </span>
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="4dc0fb67-c430-44a9-85c5-ef2885fb1a30">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         Using FT can bypass the often intractable integral computation of convolution.
        </strong>
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="3f1b5493-70ae-4f6f-822d-09a9779eec65">
     <strong>
      Thus, FNO designs the kernel integral operator using FT for the neural operator layer.
     </strong>
    </p>
    <figure class="image" id="1702d833-3f93-4a12-b823-82ac0d517419">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_2.27.49_PM.png" style="width:624px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2108.08481">
       Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="e267fbe5-970e-4b7f-84bd-f6fbf6055f73">
     <li class="notion-li" style="list-style-type:disc">
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        R_{\phi}
       </span>
      </span>
      : Learnable transform directly parameterized in Fourier space.
      <ul class="bulleted-list" id="a767ab3e-7c9f-49b9-952e-1e9f10e81a9f">
       <li class="notion-li" style="list-style-type:circle">
        Complex-valued
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          (k_{\max} ×d_v ×d_v)
         </span>
        </span>
        -tensor is differentiable in torch.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="3f6ccaef-316e-4820-9e6e-dd21db9ac564">
     <li class="notion-li" style="list-style-type:disc">
      FFT is also differentiable in torch:
      <a href="https://pytorch.org/docs/stable/fft.html">
       torch.fft — PyTorch 2.3 documentation
      </a>
      .
     </li>
    </ul>
    <h3 class="" id="2dca303f-4d46-457a-8db6-934b64dcb8f6" style="font-weight: 650;">
     Theoretical Consideration
    </h3>
    <p class="notion-paragraph" id="7a8a0f5d-2b12-4297-b72e-060871debf0c">
     <strong>
      Note
     </strong>
    </p>
    <ul class="bulleted-list" id="47f57f23-9ffe-458a-b0cb-11444209a11c">
     <li class="notion-li" style="list-style-type:disc">
      For proof, lifting and projection operators
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        Q
       </span>
      </span>
      ,
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        P
       </span>
      </span>
      are set to the identity. The authors found learning these operators beneficial in practice. Further proof to cover this is required.
     </li>
    </ul>
    <ul class="bulleted-list" id="c1dcf7f6-9945-42c5-a09e-bc1cec6877fa">
     <li class="notion-li" style="list-style-type:disc">
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \text{NO}_n
       </span>
      </span>
      : Neural operator network with
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        n
       </span>
      </span>
      layers.
     </li>
    </ul>
    <p class="notion-paragraph" id="f1d26887-3d55-494d-a744-4e9f43b20f4e">
     <strong>
      Approximation Theorem
     </strong>
    </p>
    <figure class="image" id="84e3afd9-cc92-40e6-9d9d-f57fdeebb623">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_6.29.01_PM.png" style="width:672px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2108.08481">
       Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="295fe2a2-ebe2-40ce-b540-64051af6fda3">
     <li class="notion-li" style="list-style-type:disc">
      This theorem states that a NO network with sufficient depth
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        N
       </span>
      </span>
      can approximate any target operator
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        G^\dagger
       </span>
      </span>
      within a specified tolerance.
     </li>
    </ul>
    <ul class="bulleted-list" id="505d3f1c-c502-4df6-9aa0-cd310f808c8f">
     <li class="notion-li" style="list-style-type:disc">
      See Appendix G for detail.
     </li>
    </ul>
    <p class="notion-paragraph" id="61eb6613-0045-4cd9-9ebe-9002a4cb54b0">
     <strong>
      Discretization Invariance
     </strong>
    </p>
    <figure class="image" id="6afb64d0-f9de-49df-b043-39b47a2e9d65">
     <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_12.35.03_AM.png" style="width:672px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2108.08481">
       Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <ul class="bulleted-list" id="3846f8a1-2174-46d0-8c88-c7bd33914428">
     <li class="notion-li" style="list-style-type:disc">
      This theorem states that NO network achieves discretization-invariance.
     </li>
    </ul>
    <ul class="bulleted-list" id="3a5f2939-e30c-4301-8ca2-6e4cbe4859f8">
     <li class="notion-li" style="list-style-type:disc">
      See Appendix E for detail. But let’s clarify one important thing here:
      <strong>
       How to define discretization-invariant?
      </strong>
      <figure class="image" id="d98d4cea-1b84-476e-ae2c-37e44d08babe">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_9.43.03_PM.png" style="width:672px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/2108.08481">
         Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="f6907ca8-5a09-4457-8659-5b1bcaa15a11">
       <li class="notion-li" style="list-style-type:circle">
        Even after obtaining trained
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \mathcal G
         </span>
        </span>
        , evaluating
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \mathcal G(a)
         </span>
        </span>
        is some what theoretical: Since
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \mathcal G
         </span>
        </span>
        is operator, input should be a function
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          a
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="5cfeb05c-c7d0-44dc-a6f6-0a57b6594c8c">
       <li class="notion-li" style="list-style-type:circle">
        To pass a function as input, we need to evaluate
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          a
         </span>
        </span>
        on discretization
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          D_L
         </span>
        </span>
        with
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          L
         </span>
        </span>
        points.
        <ul class="bulleted-list" id="9de590d9-a720-41ca-bc32-56fd5b4e0870">
         <li class="notion-li" style="list-style-type:square">
          With points
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            \{ x_1,\cdots,x_L \}
           </span>
          </span>
          , we pass function
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            a
           </span>
          </span>
          as evaluated points
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            \{ a(x_1),\cdots, a(x_L) \}
           </span>
          </span>
          to
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            \mathcal G
           </span>
          </span>
          .
         </li>
        </ul>
       </li>
      </ul>
      <ul class="bulleted-list" id="a8025d9f-afed-478a-b3c2-27eeabb196a7">
       <li class="notion-li" style="list-style-type:circle">
        We denote this discretized evaluation of
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          a
         </span>
        </span>
        for
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \mathcal G
         </span>
        </span>
        as
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \hat{\mathcal G}(D_L, a|_{D_L})
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="2b37c749-12a3-4275-86fe-dfee318e4f84">
       <li class="notion-li" style="list-style-type:circle">
        If the distance between
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \hat{\mathcal G}(D_L, a|_{D_L})
         </span>
        </span>
        and
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \mathcal G(a)
         </span>
        </span>
        in a function space,
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          R_K(\mathcal G, \hat{\mathcal G}, D_L)
         </span>
        </span>
        , is small for sufficiently large discretization size, then we can say
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          \mathcal G
         </span>
        </span>
        is discretization-invariant.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="8b7bc533-6eb1-4fef-a94e-797533872f53">
     <li class="notion-li" style="list-style-type:disc">
      Note that model prediction with trained NO becomes
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \hat{\mathcal G}(D_L, a|_{D_L})(D_L)
       </span>
      </span>
      naturally: e.g. mapping
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        \hat{\mathcal G}(D_L, a|_{D_L})(D_L) : u_0(D_L) \mapsto u_t(D_L)
       </span>
      </span>
      .
      <figure class="image" id="b88fcea1-b699-4c84-9086-1d6870754ec2" style="text-align:center">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-12_at_11.35.29_PM.png" style="width:624px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/2108.08481">
         Neural Operator: Learning Maps Between Function Spaces (arxiv.org)
        </a>
       </figcaption>
      </figure>
     </li>
    </ul>
    <hr id="161808d5-fff6-4c81-b455-0ad291839a38"/>
    <h2 class="" id="049d0c37-742b-45ac-9605-7faa8301f1b2" style="font-weight: 650;">
     Application of Neural Solvers
    </h2>
    <p class="notion-paragraph" id="d02a57de-0f1e-49bd-a8b6-b1693cb56255">
     We will conclude this series of posts, “Neural Solver Towards the Future of Simulation,” by briefly introducing application papers on neural solvers. Figures in this section are from the respective papers.
    </p>
    <ul class="bulleted-list" id="dfe4cc6d-74e1-43d3-aa9c-5d2d9f985da2">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       <a href="https://arxiv.org/abs/2202.11214">
        FourCastNet: A Global Data-driven High-resolution Weather Model using Adaptive Fourier Neural Operators
       </a>
      </strong>
      <p class="notion-paragraph" id="3695a71c-e620-480d-bac0-6717b4844a51">
       Developed by NVIDIA, FourCastNet utilizes the Adaptive Fourier Neural Operator (AFNO) to predict weather forecasts with high resolution.
      </p>
      <figure class="image" id="fd36bb2d-7cd7-4817-aab8-4d9d438c7f35">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/FourCastNet.gif" style="width:528px"/>
      </figure>
      <ul class="bulleted-list" id="43194451-8267-44d2-baca-45bb41392630">
       <li class="notion-li" style="list-style-type:circle">
        ERA5 serves as the ground truth for comparison.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="63e118a6-cf4f-4e25-9250-34033234d004">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       <a href="https://openreview.net/forum?id=xuY33XhEGR">
        ClimODE: Climate and Weather Forecasting with Physics-informed Neural ODEs
       </a>
      </strong>
      <strong>
       | ICLR 2024 Oral
      </strong>
      <p class="notion-paragraph" id="12c4215c-47c2-4d13-8c54-599e3682317e">
       ClimODE represents 2nd-order PDEs as a system of 1st-order ODEs to apply the Neural ODE framework.
      </p>
      <figure class="image" id="45f7555b-379a-4126-8323-0d776d1c9e50">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_6.44.38_PM.png" style="width:624px"/>
      </figure>
      <figure class="image" id="f2320d25-1fa2-4e0b-93b6-8df1be5d0cca">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_6.42.56_PM.png" style="width:1772px"/>
      </figure>
      <ul class="bulleted-list" id="f00c3c50-2f0a-448a-ae01-6ca861532302">
       <li class="notion-li" style="list-style-type:circle">
        NN
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          f_\theta
         </span>
        </span>
        is given as:
        <figure class="image" id="97302c1b-f51f-4bfd-9b13-7b7890a72675">
         <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_6.45.36_PM.png" style="width:596px"/>
        </figure>
        <figure class="image" id="8e44cd49-31ab-4dbc-94d8-aa180330bf06">
         <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Untitled 1.png" style="width:652px"/>
        </figure>
       </li>
      </ul>
      <ul class="bulleted-list" id="4ece8d94-00e0-4042-9992-e082e960fcb2">
       <li class="notion-li" style="list-style-type:circle">
        Project Page:
        <a href="https://yogeshverma1998.github.io/ClimODE/">
         ClimODE (yogeshverma1998.github.io)
        </a>
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="40d33e1a-4489-4cb9-97e5-f66181a61fc3">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       <a href="https://openreview.net/forum?id=ZZTkLDRmkg">
        BENO: Boundary-embedded Neural Operators for Elliptic PDEs
       </a>
      </strong>
      <strong>
       | ICLR 2024
      </strong>
      <p class="notion-paragraph" id="378ff446-fd7d-4aef-b80c-39b94f74dbff">
       This neural operator architecture addresses the challenges posed by complex boundary geometries.
      </p>
      <figure class="image" id="aad83cc8-e117-4e23-b690-f0c643beb4e6">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_7.15.35_PM.png" style="width:624px"/>
      </figure>
      <p class="notion-paragraph" id="e6e640f5-cf2b-4639-b7ec-e85ebbf4b344">
       The dual-branch design builds two different types of edges on the same graph separately.
      </p>
      <figure class="image" id="947446d8-421f-4b71-acac-616334d1a233">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_7.24.14_PM.png" style="width:624px"/>
      </figure>
      <figure class="image" id="bdf58743-c0d7-401b-b302-ded672bdec4b">
       <img src="/pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content/Screenshot_2024-07-11_at_7.24.58_PM.png" style="width:624px"/>
      </figure>
      <ul class="bulleted-list" id="6c3bdb65-cd22-45c6-9a3f-3a6ffb4074ec">
       <li class="notion-li" style="list-style-type:circle">
        Branch 1 considers the effects of interior nodes, governed by the Laplacian constraint.
       </li>
      </ul>
      <ul class="bulleted-list" id="f4363375-321f-4394-90a9-d39faff604e8">
       <li class="notion-li" style="list-style-type:circle">
        Branch 2 focuses solely on how to propagate the relationship between boundary values and interior nodes in the graph, dominated by the harmonic solution (zero Laplacian).
       </li>
      </ul>
     </li>
    </ul>
   </div>
  </article>
  <span class="sans" style="font-size:14px;padding-top:2em">
  </span>
 </body>
</html>
