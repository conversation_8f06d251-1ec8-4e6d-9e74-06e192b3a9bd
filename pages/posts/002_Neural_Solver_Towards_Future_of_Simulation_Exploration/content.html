---
layout: page
permalink: pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content.html
---
<html>
 <head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
  <title>
   Neural Solver Towards Future of Simulation: Exploration
  </title>
 </head>
 <body>
  <article class="page sans" id="49ba991e-c1fc-4c20-87c2-09c53f1bb67a">
   <header>
    <h1 class="page-title" style="font-weight: 650; margin-top: 0px;">
     Neural Solver Towards Future of Simulation: Exploration
    </h1>
    <h6 style="font-weight: 300; font-size: 0.9em;">
     Date: 08-09-2024 | Author: Ki-Ung Song
    </h6>
    <p class="notion-paragraph">
    </p>
   </header>
   <div class="page-body">
    <details style="padding-bottom: 10px;">
     <summary>
      <span style="font-weight: 650;">
       Neural Solver Towards Future of Simulation Series
      </span>
     </summary>
     <ul class="bulleted-list">
      <li class="notion-li">
       <a href="{{ site.baseurl }}{% link pages/posts/003_Neural_Solver_Towards_Future_of_Simulation_Deep_Dive/content.html %}">
        <div>
         Neural Solver Towards Future of Simulation: Deep Dive
        </div>
       </a>
      </li>
      <li class="notion-li">
       <div>
        <u>
         Neural Solver Towards Future of Simulation: Exploration - Current Post
        </u>
       </div>
      </li>
      <li class="notion-li">
       <a href="{{ site.baseurl }}{% link pages/posts/001_Neural_Solver_Towards_Future_of_Simulation_Intro/content.html %}">
        <div>
         Neural Solver Towards Future of Simulation: Intro
        </div>
       </a>
      </li>
     </ul>
    </details>
    <nav class="table_of_contents" id="e932b8d5-4c70-418f-8903-63d1846322b7">
     <details>
      <summary>
       <span style="font-weight: 650; white-space: nowrap;">
        Table of Contents
       </span>
      </summary>
      <div style="padding-top: 10px; padding-bottom: 10px;">
       <div class="table_of_contents-item table_of_contents-indent-0">
        <a class="table_of_contents-link" href="#371af6e4-3c80-41b4-909a-20725e92d946">
         Neural ODE
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#3ff7ddeb-69a4-45bd-b86e-b55d8b3ea45e">
         Motivation
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#24d8ef38-3d7b-46ed-8bd8-deba79645391">
         Forward Computation
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#2dbfea45-68f3-4ae1-ba56-f5fc722c7e48">
         Backward Computation
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#26ce2f17-336a-4e24-9b06-7b83e24a9616">
         Application
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-0">
        <a class="table_of_contents-link" href="#4d7ef08d-0d5e-46dc-8f51-a30aca4e460b">
         PINN (Physics-Informed Neural Network)
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#9c054677-e929-4ffc-86e9-9f48216f03e4">
         INR (
         Implicit Neural Representation)
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#ccf3be0f-d0be-4a04-8a14-a474d31926bb">
         Overview
        </a>
       </div>
       <div class="table_of_contents-item table_of_contents-indent-1">
        <a class="table_of_contents-link" href="#89f412ce-91dd-44fe-ab90-4c5f282d2b76">
         Related Topics
        </a>
       </div>
      </div>
     </details>
    </nav>
    <p class="notion-paragraph" id="bd32404a-96c4-451c-a539-65f74fc5ea94">
     Building on the foundational insights from our last post on neural solvers, we explore two main neural solver approaches in this post:
    </p>
    <ol class="numbered-list" id="0373bc94-f874-4938-bf4f-3dfda28e1dba" start="1" type="1">
     <li class="notion-li">
      <strong>
       Neural Ordinary Differential Equation (Neural ODE)
      </strong>
     </li>
    </ol>
    <ol class="numbered-list" id="862e5eac-8aa6-423b-9886-e6ebd0c23812" start="2" type="1">
     <li class="notion-li">
      <strong>
       Physics-Informed Neural Network (PINN)
      </strong>
     </li>
    </ol>
    <hr id="0d1b7513-2672-44be-b04f-57838ed7a3d1"/>
    <h2 class="" id="371af6e4-3c80-41b4-909a-20725e92d946" style="font-weight: 650;">
     Neural ODE
    </h2>
    <h3 class="" id="3ff7ddeb-69a4-45bd-b86e-b55d8b3ea45e" style="font-weight: 650;">
     Motivation
    </h3>
    <p class="notion-paragraph" id="88b15a19-217f-487b-b7bd-a6f78b54d76c">
     The literature presents numerous variants of Neural ODE. However, this post will be anchored in the foundational principles outlined in the original paper:
     <a href="https://arxiv.org/abs/1806.07366">
      Neural Ordinary Differential Equations (arxiv.org)
     </a>
    </p>
    <p class="notion-paragraph" id="1ac8199c-4efc-40bf-a268-62a161853242">
     Let’s start rethinking the residual connection:
    </p>
    <figure class="equation" id="3e1a7b11-f655-4432-8c19-9134e5d10d23">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        z_{t+1} = z_t + f(z_t, θ_t)
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="207bcd6f-0e2c-48b5-8fc3-a005090dc341">
     where
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       t ∈ \{0, \dots , T\}
      </span>
     </span>
     ,
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       z_t ∈ \R^D
      </span>
     </span>
     is hidden state of
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       t
      </span>
     </span>
     -th layers, and
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \theta_t
      </span>
     </span>
     is
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       t
      </span>
     </span>
     -th layers weight.
    </p>
    <p class="notion-paragraph" id="31d78876-26c4-4879-a72f-95912e92e2cf">
     Recall the previously introduced Euler method:
    </p>
    <figure class="equation" id="8b6ba50b-82c4-4139-a00b-6a9a022216c5">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        y_{n+1} = y_n + hf(t_n, y_n)
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="20d42a32-4627-4045-8052-583b7ec7611d">
     By comparing this with the residual connection, we see a clear similarity: the residual connection can be interpreted as a single step of the Euler method with step-size
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       h=1
      </span>
     </span>
     .
    </p>
    <p class="notion-paragraph" id="13ed6ac8-9590-4a70-bd78-d36104f9bcaf">
     This allows transitioning from discrete layers to a continuous-time formulation, parameterizing the continuous dynamics between hidden states as evolving continuously over time:
    </p>
    <figure class="equation" id="173f1758-b264-4d8c-8a16-fb974312aace">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \begin{align}\dfrac{dz(t)}{dt} = f_θ(z(t), t) \tag{1}\end{align}
       </span>
      </span>
     </div>
    </figure>
    <ul class="bulleted-list" id="39e6f8fe-5e3f-44f4-b653-1b60c50eb932">
     <li class="notion-li" style="list-style-type:disc">
      Assume
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        t\in[0,1]
       </span>
      </span>
      .
     </li>
    </ul>
    <ul class="bulleted-list" id="bab97cc7-c764-4404-b219-31d995a5b167">
     <li class="notion-li" style="list-style-type:disc">
      Note that this can also be interpreted as a NN with infinite depth.
     </li>
    </ul>
    <h3 class="" id="24d8ef38-3d7b-46ed-8bd8-deba79645391" style="font-weight: 650;">
     Forward Computation
    </h3>
    <p class="notion-paragraph" id="975f2afc-5c05-4087-857c-8b84314f7860">
     Now we have a sophisticated formulation for NN. But how can we compute the output of this DL model explicitly? While this continuous-time approach appears elegant, it's not immediately obvious how to calculate the model's output.
    </p>
    <ul class="bulleted-list" id="1c1bac39-1967-46e8-9f56-222a207b2395">
     <li class="notion-li" style="list-style-type:disc">
      Mathematical Solution:
      <p class="notion-paragraph" id="d0f1e42e-0eb4-4ed9-9fc9-60b1695618f7">
       Given initial value
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         z(t_0)=z(0)
        </span>
       </span>
       , the model output
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         z(t_N)=z(1)
        </span>
       </span>
       is obtained via simply integrating the both side of (1):
      </p>
      <figure class="equation" id="49beebe0-2e29-444d-bb17-15994c148542">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          z(t_N) = z(0) +  \int_{t_0}^{t_N} f_θ(z(t), t) \ dt \tag{2}
         </span>
        </span>
       </div>
      </figure>
      <p class="notion-paragraph" id="79a088c4-c3e2-45e6-93a5-cbf696f495c8">
       But you should know that integration is not a “simple” process in the real world. Integration is only mathematically meaningful, but in reality, it often turns out to be a shiny but impractical concept.
      </p>
      <p class="notion-paragraph" id="6f837bef-dadb-49ca-96b9-064133cf9ca8">
       Actually, solving the differential equation is equivalent to approximating the integral in many cases. We can express the approximation of the RHS in (2) with
       <code>
        ODESolve()
       </code>
       operation. It leads to the following expression:
      </p>
      <figure class="equation" id="2865642f-3ee8-471e-9918-8ab1efb0edc0">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          z(t_N)=\text{ODESolve}(z(t_0), f_{\theta}, t_0, t_N)
         </span>
        </span>
       </div>
      </figure>
      <p class="notion-paragraph" id="000acb3a-bcf5-4f57-a086-6f902203ce11">
       Assume that this kind of approximation is computed by
       <strong>
        black-box
       </strong>
       called “ODE solver”.
      </p>
      <ul class="bulleted-list" id="9f7e2929-3af4-4447-a792-c94a416c1678">
       <li class="notion-li" style="list-style-type:circle">
        ODE solver includes basic methods like Euler method, but in practice, more sophisticated methods such as Runge-Kutta are used.
       </li>
      </ul>
      <ul class="bulleted-list" id="c5965fff-e579-484d-9735-56ee97ec2a29">
       <li class="notion-li" style="list-style-type:circle">
        Only two observations are possible: Input
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          z_0
         </span>
        </span>
        at the beginning
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          t_0
         </span>
        </span>
        and output
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          z_1
         </span>
        </span>
        at the end of the trajectory
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          t_N
         </span>
        </span>
        since ODE solver is a black-box.
       </li>
      </ul>
     </li>
    </ul>
    <h3 class="" id="2dbfea45-68f3-4ae1-ba56-f5fc722c7e48" style="font-weight: 650;">
     Backward Computation
    </h3>
    <p class="notion-paragraph" id="2f9221ac-4a5b-4a0a-b1ff-802a2dbc8197">
     But due to its black-box nature, typical
     <strong>
      gradient computation becomes infeasible
     </strong>
     .
    </p>
    <ul class="bulleted-list" id="0bbc17a3-5d5b-4054-8de1-a715537122a1">
     <li class="notion-li" style="list-style-type:disc">
      It means that
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        {\partial z(t_N)}/\partial \theta
       </span>
      </span>
      is not obtainable with the given
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        z(t_N)
       </span>
      </span>
      , so we cannot apply chain rule to get
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        {\partial L}/{\partial \theta}
       </span>
      </span>
      for weight update.
     </li>
    </ul>
    <p class="notion-paragraph" id="c465c731-b190-4d05-be57-6123afd1c949">
     Then how can we actually train this kind of DL model? How do we apply back-propagation?
     <strong>
      The main idea to tackle this is to approximate the gradient
     </strong>
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       {\partial L}/{\partial \theta}
      </span>
     </span>
     <strong>
      for back-propagation.
     </strong>
    </p>
    <p class="notion-paragraph" id="6f6c41a8-d5fc-4af7-bd4d-e5ca0d9e9afd">
     Let’s find out how this becomes possible. Before that we can typical assume supervised loss with the model’s output as:
    </p>
    <figure class="equation" id="13fbcbae-77bb-4c8b-b119-aac4cc6b2b07">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal L(\theta) \approx L(z(t_N), y_{true}) \eqqcolon L(z(t_N))
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="498d6eb8-3311-4363-9f84-1f08414ae71e">
     This is expressed as
    </p>
    <figure class="equation" id="43b867f4-86ab-45c0-8a5a-e2db2b9e37f7">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        L(z(t_N)) = L \big( \text{ODESolve}(z(t_0), f_{\theta}, t_0, t_N) \big)
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="bc9bedd7-a88b-44fd-97d5-a2e2a9c1dff4">
     Recall that our goal is to approximate the gradients of
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       L
      </span>
     </span>
     with respect to its parameters
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \theta
      </span>
     </span>
     . We achieve this through step-by-step computation of various quantities, as illustrated in the figure below. You may not understand this now, but it will become clear soon.
    </p>
    <figure class="image" id="c5e8c909-feca-43b8-b99f-8848c6025283" style="text-align:center">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Untitled.png" style="width:480px"/>
     <figcaption>
      <a href="https://medium.com/@justygwen/neural-ordinary-differential-equations-neural-odes-rethinking-architecture-272a72100ebc">
       Neural Ordinary Differential Equations (Neural ODEs): Rethinking Architecture | by Gwen Xiao | Medium
      </a>
     </figcaption>
    </figure>
    <p class="notion-paragraph" id="cb5f7f54-b94a-4971-a5e5-5528983d27a2">
     <strong>
      Step 1: Focus on what we can compute and generalize it
     </strong>
    </p>
    <p class="notion-paragraph" id="08af5720-0342-4db3-b819-023c7b844917">
     One quantity is straightforward to compute:
    </p>
    <figure class="equation" id="290b2063-1494-4e5e-87f6-cc511f90549c">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \dfrac{\partial L}{\partial z(t_N)} \tag{*}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="3e703dbd-2c1b-4ce9-9861-5e7ba9717bad">
     Let’s generalize the above quantity to a component called
     <strong>
      adjoint
     </strong>
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a(t)
      </span>
     </span>
     :
    </p>
    <figure class="equation" id="5768ccd3-5501-43cc-98bc-e8bf4455a2de">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        a(t) = \dfrac{\partial L}{\partial z(t)}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="1164cc18-3330-4b02-b79c-e8b3c4220cc9">
     The above quantity (*) is now expressed as a component
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a(t_N)
      </span>
     </span>
     .
    </p>
    <p class="notion-paragraph" id="c046e1fb-288c-4dad-89fd-c27e212647ec">
     Then the evolving dynamics of
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a(\cdot)
      </span>
     </span>
     can be derived as follows
    </p>
    <figure class="equation" id="414563a4-310f-4560-aaeb-da9b3f5f4705">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \dfrac{d a(t)}{d t} = -a(t) \dfrac{\partial f_\theta(z(t), t)}{\partial z(t)} \tag{3}
       </span>
      </span>
     </div>
    </figure>
    <ul class="bulleted-list" id="09d0f0f7-0f76-4b3a-89fe-85adaa4994f3">
     <li class="notion-li" style="list-style-type:disc">
      For derivation details, please refer to the original paper. This post will focus on providing a conceptual overview for better understanding.
     </li>
    </ul>
    <p class="notion-paragraph" id="dac7c3ba-0de1-4a77-ad1e-31c6b0be809f">
     By applying integration to LHS of (3) and properly substituting with (3), the following can be obtained:
    </p>
    <figure class="equation" id="a8e0b089-cbab-49f1-8342-1027a80b0dbc">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        a(t_0) = a(t_N) + \int_{t_N}^{t_0} \dfrac{da(t)}{dt} dt = a(t_N) - \int_{t_N}^{t_0} a(t) \dfrac{\partial f_\theta(z(t), t)}{\partial z(t)} dt \tag{4}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="f6a3ea70-4f93-409b-b842-01b1c0965f82">
     <strong>
      What’s the meaning of this?
     </strong>
    </p>
    <ul class="bulleted-list" id="3f0a9354-2117-4ee5-a927-7f55336032a5">
     <li class="notion-li" style="list-style-type:disc">
      The derived dynamics (3) is a form of ODE and from the last post, we know that the ODE can be solved reversed starting from
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        a(t_N)
       </span>
      </span>
      . And it is equivalent to computing the integration in RHS of (4).
     </li>
    </ul>
    <ul class="bulleted-list" id="32b7e29b-7f9b-4cf5-9478-c55cfd1e3002">
     <li class="notion-li" style="list-style-type:disc">
      From the discussion of previous section we can expect that the integration in RHS of (4) can be approximated via ODE solver.
     </li>
    </ul>
    <p class="notion-paragraph" id="24f665ba-89fa-43cf-accf-a9810617b3ab">
     <strong>
      Step 2: Generalize again to other quantities
     </strong>
    </p>
    <p class="notion-paragraph" id="0a16e83f-a4b2-4fea-bbf0-9072313d8acd">
     We can define the following quantities assuming
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a_{θ}(t_N)=0
      </span>
     </span>
     , and
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \theta=\theta(t) \quad \forall t\in[0,1)
      </span>
     </span>
     :
    </p>
    <figure class="equation" id="0e962571-d60b-4d2d-b512-7804ac09049d">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        a_{θ}(t) := \dfrac{\partial L}{\partial θ(t)}, \quad a_{t}(t) := \dfrac{\partial L}{\partial t(t)}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="e6f17e85-be92-4d29-bfdd-60fc0ba8443e">
     The corresponding dynamics can be similarly derived:
    </p>
    <figure class="equation" id="5c34bcc2-d141-4557-93fe-1383c7e60a20">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \dfrac{d a_{\theta}(t)}{d t} = -a(t) \dfrac{\partial f_\theta(z(t), t)}{\partial \theta} \tag{5}
       </span>
      </span>
     </div>
    </figure>
    <figure class="equation" id="ed72433c-b629-4ad3-8e59-d7074624813f">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \dfrac{d a_t(t)}{d t} = -a(t) \dfrac{\partial f_\theta(z(t), t)}{\partial t} \tag{6}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="2fad69db-20fb-4022-aedc-b3cf6069d624">
     It leads to the following by integrating the both side:
    </p>
    <figure class="equation" id="64df9cdb-1320-45c8-bf56-a7421a0d9293">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \dfrac{\partial L}{\partial \theta} = a_{θ}(t_0) = -\int_{t_N}^{t_0} a(t) \dfrac{\partial f_\theta(z(t), t)}{\partial \theta} dt \tag{7}
       </span>
      </span>
     </div>
    </figure>
    <figure class="equation" id="41885fc2-fd29-47b1-b7f8-1fbf9518f486">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \dfrac{\partial L}{\partial t_0} = a_{t}(t_0) = a_{t}(t_N) -\int_{t_N}^{t_0} a(t) \dfrac{\partial f_\theta(z(t), t)}{\partial t} dt \tag{8}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="0c421cd5-26f4-4207-8b62-78edc62ef050">
     And note that since
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a(t_N)
      </span>
     </span>
     is available, we can compute
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       a_{t}(t_N)
      </span>
     </span>
     explicitly by using (1):
    </p>
    <figure class="equation" id="1dfabf9b-e105-4854-83b9-05a94c45eacd">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        a_{t}(t_N) = \dfrac{\partial L}{\partial t_N} = \dfrac{\partial L}{\partial z(t_N)} \dfrac{\partial z(t_N)}{\partial t(t_N)} = a(t_N) f_\theta(t_N,z(t_N))
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="6b6c7ca8-922e-40f2-b02e-7c7a630a1bba">
     We can now see that solving the equation (5) reversely via computing the integration of RHS in (7) leads to the desired approximated
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       {\partial L}/{\partial \theta}
      </span>
     </span>
     .
    </p>
    <p class="notion-paragraph" id="74bcf7f0-42b7-45c5-936a-936e417bf885">
     <strong>
      Step 3: Compute the gradient via reverse ODE solving
     </strong>
    </p>
    <p class="notion-paragraph" id="17b7c6f4-4346-40a0-a10e-be2ef6d8f2d0">
     For reverse ODE solving, ODE solver can be used again. It should be applied to solve the following systems of the equations resulting from (1)~(8):
    </p>
    <figure class="equation" id="104c6442-825a-447d-a48a-833fa60aa124">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \begin{cases}\dot z=f_\theta(z,t),
\\
\dot a = - a \cdot \dfrac{\partial f_\theta(z,t)}{\partial z},\\
\dot a_{\theta} = - a \cdot \dfrac{\partial f_\theta(z,t)}{\partial \theta},\\
\dot a_t = - a \cdot \dfrac{\partial f_\theta(z,t)}{\partial t}.
\end{cases}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="8528eb3f-9bcc-420e-a1b6-6c34840b2992">
     where initial values are given as:
     <div class="indented">
      <figure class="equation" id="67aaa080-5b6a-4706-83b4-1ed29b6d98e2">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          \begin{cases}
z(t_T)= \text{Obtained at the forward pass}\\
a(t_N)=\dfrac{\partial L}{\partial z(t_N)},\\
a_{θ}(t_N)=0,\\
a_t(t_N)=a(t_N) f_\theta(t_N,z(t_N)).
\end{cases}
         </span>
        </span>
       </div>
      </figure>
     </div>
    </p>
    <p class="notion-paragraph" id="0bb067a3-826c-44bf-a84d-639ac338730b">
     with
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \dot{a}
      </span>
     </span>
     is abbrev. notation for
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       da(t)/dt
      </span>
     </span>
     . Same notation for others.
    </p>
    <ul class="bulleted-list" id="ff73f4c4-fba0-474c-9f08-c8584856f4c1">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Why do we need to solve these many equations simultaneously?
      </strong>
      <p class="notion-paragraph" id="b285869c-818c-4d81-ad41-d94bca2e792e">
       As can be checked in the formula, the desired dynamics for
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         a_{\theta}
        </span>
       </span>
       depends on
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         a
        </span>
       </span>
       . Then the dynamics for
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         a
        </span>
       </span>
       depends on
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         z
        </span>
       </span>
       . So they need to be passed to the ODE solver simultaneously for proper computation.
      </p>
     </li>
    </ul>
    <ul class="bulleted-list" id="a9c069b1-66d4-4859-b44d-a1ef29b798fe">
     <li class="notion-li" style="list-style-type:disc">
      Note that the dynamics
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        a_t(t)
       </span>
      </span>
      can be viewed as a just dummy for gradient computation process. But this post followed the original paper’s style.
     </li>
    </ul>
    <p class="notion-paragraph" id="dff5f6d1-d02d-449d-9f7c-b266b7c4a221">
     Now I believe that we can understand the above figure and the below algorithm presented in the original paper.
    </p>
    <figure class="image" id="e3472563-e22c-4eb1-8e0a-7c4c498c0090">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Untitled 1.png" style="width:1624px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/1806.07366">
       Neural Ordinary Differential Equations (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <p class="notion-paragraph" id="e3ed45b4-3a14-463a-a1e5-50e7207096c4">
     Please refer to this code snippets for detailed implementation code:
     <a href="https://github.com/msurtsukov/neural-ode/blob/master/Neural%20ODEs.ipynb">
      neural-ode/Neural ODEs.ipynb at master · msurtsukov/neural-ode (github.com)
     </a>
    </p>
    <h3 class="" id="26ce2f17-336a-4e24-9b06-7b83e24a9616" style="font-weight: 650;">
     Application
    </h3>
    <ul class="block-color-default bulleted-list" id="b8e3d2f7-390f-4674-9650-cb43833dcdbe">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Modern Neural ODE
      </strong>
      <p class="notion-paragraph" id="cb3e4444-b990-464a-a492-56d2ef76bb90">
       The above process for approximating gradients may seem complicated and limited, but thanks to many, the
       <strong>
        ODE solver is no longer a black box. Differentiable ODE solvers are now available.
       </strong>
      </p>
      <ul class="bulleted-list" id="390862c6-163e-4b00-852a-88628235a504">
       <li class="notion-li" style="list-style-type:circle">
        <a href="https://github.com/rtqichen/torchdiffeq">
         rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation.
        </a>
       </li>
      </ul>
      <p class="notion-paragraph" id="7001e252-457f-4db9-83ee-44fc6fbce727">
       We can now apply standard backpropagation in DL as usual. So, why introduce the earlier process? I believe that understanding the original Neural ODE derivation is valuable mathematical practice.
      </p>
      <p class="notion-paragraph" id="abe47310-4ea9-4744-9e9a-999adb656c7f">
       And although differentiable ODE solvers are now available, they can’t be too heavy to apply to Neural ODEs. So, further breakthroughs are still needed.
      </p>
     </li>
    </ul>
    <ul class="bulleted-list" id="95afe4e5-5fba-4868-9f93-8ef1272df4b1">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Normalizing Flow
      </strong>
      <figure class="image" id="33b59fd6-d26c-48c5-a3c0-e013ea1ad6ac">
       <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-08_at_11.17.20_PM.png" style="width:617px"/>
       <figcaption>
        <a href="https://lilianweng.github.io/posts/2018-10-13-flow-models/">
         Flow-based Deep Generative Models | Lil'Log (lilianweng.github.io)
        </a>
       </figcaption>
      </figure>
      <ul class="bulleted-list" id="639bf07c-a8fd-4b00-9aeb-aeff4e68b902">
       <li class="notion-li" style="list-style-type:circle">
        Normalizing flow is a generative model framework which involves an invertible function
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          f
         </span>
        </span>
        between the data distribution space
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          X
         </span>
        </span>
        and latent space
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          Z
         </span>
        </span>
        .
       </li>
      </ul>
      <ul class="bulleted-list" id="cae7216f-51d3-40e7-8d5e-b8a21256e33b">
       <li class="notion-li" style="list-style-type:circle">
        Neural ODEs are naturally invertible due to the properties of ODEs. This allows training them in the
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          x\mapsto z
         </span>
        </span>
        direction and solving them in the
        <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
         <span class="equation_math">
          z\mapsto x
         </span>
        </span>
        direction for generation, known as
        <strong>
         continuous normalizing flow.
        </strong>
       </li>
      </ul>
      <ul class="bulleted-list" id="bbe48c5a-7f8d-44b5-803b-3d2bc2c3a42e">
       <li class="notion-li" style="list-style-type:circle">
        This concept is related to the current flow-matching framework for diffusion models.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="762af4b0-7dcf-42cf-839a-ff68eed502d9">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Solving DEs
      </strong>
      <ul class="bulleted-list" id="b56910c9-99cb-4c81-a326-e38414267fbd">
       <li class="notion-li" style="list-style-type:circle">
        Due to their design, Neural ODEs offer a good inductive bias for modeling and solving the dynamics of differential equations
       </li>
      </ul>
      <ul class="bulleted-list" id="72430d9b-f7ed-4ccd-b1f7-c6df43133fd9">
       <li class="notion-li" style="list-style-type:circle">
        Neural ODEs can be utilized as a key component in frameworks for Neural Solvers, enhancing their capability to handle complex differential equations efficiently.
       </li>
      </ul>
      <ul class="bulleted-list" id="ea697d88-6ca3-43b4-b8a8-6e08922943f3">
       <li class="notion-li" style="list-style-type:circle">
        Cases:
        <ul class="bulleted-list" id="7c085276-7a87-4cd5-a0c8-db41414eb476">
         <li class="notion-li" style="list-style-type:square">
          Optical Flow Estimation: Captures dynamic motion in video sequences.
         </li>
        </ul>
        <ul class="bulleted-list" id="2bb0becd-4f01-40c6-91b3-308634ab8506">
         <li class="notion-li" style="list-style-type:square">
          Control Systems: Designs and analyzes stable control systems.
         </li>
        </ul>
        <ul class="bulleted-list" id="83e94e5f-51d8-4838-9d7d-cc72401755ee">
         <li class="notion-li" style="list-style-type:square">
          Climate Modeling: Predicts and models climate dynamics.
         </li>
        </ul>
       </li>
      </ul>
     </li>
    </ul>
    <hr id="f0f88b51-0aaf-436c-8401-10abbf8f59c6"/>
    <h2 class="" id="4d7ef08d-0d5e-46dc-8f51-a30aca4e460b" style="font-weight: 650;">
     PINN (Physics-Informed Neural Network)
    </h2>
    <h3 class="" id="9c054677-e929-4ffc-86e9-9f48216f03e4" style="font-weight: 650;">
     INR (
     Implicit Neural Representation)
    </h3>
    <p class="notion-paragraph" id="b1f7a24e-4143-4e55-841f-9b639b943b0e">
     Implicit Neural Representation (INR) uses NNs to model continuous functions that implicitly represent data, providing a flexible and efficient way to approximate complex physical phenomena.
    </p>
    <ul class="block-color-default bulleted-list" id="70db1c76-575b-431e-8ab1-f7e467e96605">
     <li class="notion-li" style="list-style-type:disc">
      In other words,
      <strong>
       INR represents signals by continuous functions parameterized by NNs
      </strong>
      , unlike traditional discrete representations (e.g., pixel, mesh).
     </li>
    </ul>
    <ul class="bulleted-list" id="c96044ed-d04e-4b41-9412-efe093812650">
     <li class="notion-li" style="list-style-type:disc">
      This post introduces INR based on the following paper:
      <a href="https://arxiv.org/abs/2006.09661">
       Implicit Neural Representations with Periodic Activation Functions (arxiv.org)
      </a>
     </li>
    </ul>
    <p class="notion-paragraph" id="6a35897d-c1f0-41e3-b9d7-00e8ba95ff02">
     Typical RGB image data
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \Omega
      </span>
     </span>
     can be interpreted as a function: for spatial location
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       x\in \Omega \subset \R^2
      </span>
     </span>
     , it corresponds to a 3-dimensional RGB value
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       f(x)\in\R^3
      </span>
     </span>
     .
    </p>
    <figure class="image" id="05007e08-424c-4941-80f3-77c446267aac">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-09_at_11.54.25_PM.png" style="width:480px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2011.12026v1">
       Adversarial Generation of Continuous Images (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <p class="notion-paragraph" id="f428fdc3-a868-45ff-bfe9-332ca3ffc58f">
     Given RGB image data
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \Omega
      </span>
     </span>
     ,  INR is a coordinate-based NN that model data as the realization of an implicit function of a spatial location
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       x ∈ Ω \mapsto f_θ(x)
      </span>
     </span>
     where
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       f_θ:\R^2 \rightarrow \R^3
      </span>
     </span>
     . It approximates
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       f_\theta \approx f
      </span>
     </span>
     .
    </p>
    <ul class="bulleted-list" id="b93b9f6b-1443-423b-b585-7e75a3fe7f28">
     <li class="notion-li" style="list-style-type:disc">
      Consider a 1024x1024x3 image. Based on int8 representation, this image would typically require 1024×1024×3=3,145,728 bytes of memory.
     </li>
    </ul>
    <ul class="bulleted-list" id="5e4b6c12-33d4-4462-9484-f3f78e0249ca">
     <li class="notion-li" style="list-style-type:disc">
      And consider the above NN with 4 hidden layers of dimension 4 without bias term. Then first and last weight is
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        W_0\in \R^{2\times 4}
       </span>
      </span>
      and
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        W_4\in\R^{4 \times 3}
       </span>
      </span>
      respectively. And
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        W_i\in\R^{4\times 4}
       </span>
      </span>
      with
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        i\in\{1,2,3\}
       </span>
      </span>
      . The required memory to store the weights of this NN is 272 bytes only since there are 2x4+4x4x3+4x3=68 parameters, each needing 4 bytes (float32).
     </li>
    </ul>
    <ul class="bulleted-list" id="b4068b24-9374-4ca8-b78f-871602d06e31">
     <li class="notion-li" style="list-style-type:disc">
      If this NN can approximate the given image properly, it significantly reduces the required memory to store the data. This is a extreme example with exaggeration, but highlights the efficiency of INR.
     </li>
    </ul>
    <p class="notion-paragraph" id="c2d2097c-b94e-485d-8ee6-3e2b0f76cfb1">
     <strong>
      Continuous representation of INR can be obtained via physical on constraint of the data:
     </strong>
    </p>
    <figure class="image" id="91793bda-1cce-43b0-9c64-689fb17bc5a9">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Untitled 2.png" style="width:624px"/>
     <figcaption>
      <a href="https://arxiv.org/abs/2006.09661">
       Implicit Neural Representations with Periodic Activation Functions (arxiv.org)
      </a>
     </figcaption>
    </figure>
    <p class="notion-paragraph" id="6c42d671-c813-43cc-92ca-16cf16c3b727">
     The above figure demonstrates how INRs can leverage physical constraints to achieve continuous and high-fidelity image reconstruction and editing.
    </p>
    <ul class="bulleted-list" id="0460a8fe-d972-4a96-8b5f-2aa574e6768a">
     <li class="notion-li" style="list-style-type:disc">
      Although the NN is fitted by physical constraints like gradient (
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        ∇f(x)
       </span>
      </span>
      ) and Laplacian (
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        Δf(x)
       </span>
      </span>
      ) instead of ground truth, the reconstructions closely match the original.
     </li>
    </ul>
    <ul class="bulleted-list" id="6688cbbe-4475-4b04-96a2-0c9395f21e43">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       This demonstrates that using a loss function based on physical constraints can efficiently approximate complex physical phenomena.
      </strong>
     </li>
    </ul>
    <p class="notion-paragraph" id="7ec80dcb-e9c6-43d9-a4e9-8c3cd7b995be">
     <strong>
      Application:
     </strong>
    </p>
    <ul class="bulleted-list" id="008c80cc-60e4-4121-9916-a0980a3ae006">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       NERF
      </strong>
      <figure class="image" id="38a89e5e-7c04-4b04-941d-0377a6188808" style="text-align:center">
       <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/nerf.png" style="width:576px"/>
       <figcaption>
        <a href="https://arxiv.org/abs/2003.08934">
         NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis (arxiv.org)
        </a>
       </figcaption>
      </figure>
      <p class="notion-paragraph" id="13523ce0-9b37-446c-ac41-4dc19c715988">
       Perhaps NERF is the most famous example of an application of INRs. NERF creates continuous 5D scene representations using a Multi-Layer Perceptron (MLP) network.
      </p>
      <figure class="equation" id="b37ef7fe-5686-4dff-bf8f-ea957263ca18">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          F_{\theta} :(\mathbf{x}, \mathbf{d}) → (\mathbf{c}, \sigma)
         </span>
        </span>
       </div>
      </figure>
      <p class="notion-paragraph" id="4df3d9bc-5d36-46f9-a9a3-59d0c82759a5">
       The inputs are
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         \mathbf{x} = (x,y,z)
        </span>
       </span>
       , representing a 3D location and
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         \mathbf{d}
        </span>
       </span>
       , a 3D Cartesian unit vector. The outputs are the emitted RGB color
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         \mathbf{c} = (r,g,b)
        </span>
       </span>
       and volume density
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         \sigma
        </span>
       </span>
       .
      </p>
     </li>
    </ul>
    <ul class="bulleted-list" id="ca924a9c-7f36-4aac-81ed-b37fff1f78dc">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       PINN
      </strong>
      <p class="notion-paragraph" id="774de002-97f2-4876-8f9b-c2f9ea929dc8">
       PINNs use a loss function based on physical constraints of the given data, enabling the network to learn the data itself.
      </p>
      <p class="notion-paragraph" id="edf184d1-b7b3-4320-9224-384476c6186e">
       This concept is fundamental to the idea of PINNs, where physical laws guide the learning process for more accurate and meaningful representations of complex phenomena.
      </p>
     </li>
    </ul>
    <h3 class="" id="ccf3be0f-d0be-4a04-8a14-a474d31926bb" style="font-weight: 650;">
     Overview
    </h3>
    <p class="notion-paragraph" id="b25f4dad-4f42-41dd-a2d5-3509a49f9617">
     From the previously introduced concepts, PINNs leverage the combination of physical constraints and data fitting to learn complex physical phenomena accurately.
    </p>
    <blockquote class="" id="9f06a3f7-4b0d-47d4-b5ab-cca21faee065">
     <strong>
      The main approach of PINNs can be expressed through the following loss function construction:
     </strong>
     <figure class="equation" id="85cd97da-4d3d-4b60-8b4e-ab598cd33bab">
      <div class="equation-container">
       <span class="katex-display">
        <span class="equation_math_block">
         \mathcal L= \mathcal L_{data} +  \lambda \mathcal L_{physic}
        </span>
       </span>
      </div>
     </figure>
    </blockquote>
    <p class="notion-paragraph" id="e7e9db25-306e-4c05-a706-29b7c054243d">
     The content available at
     <a href="https://physicsbaseddeeplearning.org/physicalloss.html">
      Physical Loss Terms — Physics-based Deep Learning
     </a>
     provides good examples and illustrations, so some of them will be introduced here.
    </p>
    <p class="notion-paragraph" id="b8af9fc9-e3bc-4460-ba33-2fd657f21c36">
     Given a PDE for
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       𝑢(𝑥,𝑡)
      </span>
     </span>
     with a time evolution, we can typically express it in terms of a function
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal F
      </span>
     </span>
     of the derivatives of
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       𝑢
      </span>
     </span>
     via
    </p>
    <figure class="equation" id="a9265ba5-98ad-40d9-97f9-fe7591b6f41b">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        u_t = \mathcal F (u_{x}, u_{xx}, \cdots,u_{xx...x} ) \tag{9}
       </span>
      </span>
     </div>
    </figure>
    <ul class="bulleted-list" id="8d4eec3c-cf5d-4125-9eec-db319a3f8f91">
     <li class="notion-li" style="list-style-type:disc">
      e.g. 1D Burgers Equation
      <figure class="equation" id="cfc466d4-e7a3-4c93-b634-481622faa98b">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          \dfrac{\partial u}{\partial{t}} + u \nabla u = \nu \nabla \cdot \nabla u \tag{10}
         </span>
        </span>
       </div>
      </figure>
     </li>
    </ul>
    <p class="notion-paragraph" id="b8630292-c4e8-4b80-96d0-e93e77c56ccc">
     <strong>
      Dataset for PINNs
     </strong>
    </p>
    <p class="notion-paragraph" id="085a12ec-fda6-4d92-bcb4-a80f19a14ef2">
     The datasets used for training PINNs are typically generated by simulating the dynamics of physical system.
    </p>
    <ul class="bulleted-list" id="f875397e-1ac6-4e22-bc24-810690c234d7">
     <li class="notion-li" style="list-style-type:disc">
      Dataset structure:
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        D=\{a_i, u_i\}^N_{i=1}
       </span>
      </span>
      , where
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        a_i=(x_i,t_i)
       </span>
      </span>
      represents the spatial and temporal variables, which are the inputs to the desired solution
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u
       </span>
      </span>
      . Specifically,
      <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
       <span class="equation_math">
        u(a_i)=u_i
       </span>
      </span>
      .
     </li>
    </ul>
    <ul class="bulleted-list" id="819af0f2-b099-46bf-91f3-d06001a4335b">
     <li class="notion-li" style="list-style-type:disc">
      Burgers Equation Example:
      <p class="notion-paragraph" id="f332dc3c-e20f-453f-8079-79c33cb22e8f">
       The equation (10) is known for modeling nonlinear wave propagation and shock wave formation. The data points
       <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
        <span class="equation_math">
         (a_i,u_i)
        </span>
       </span>
       capture the evolution of the wave.
      </p>
      <figure class="image" id="3eaba786-0c83-4b73-ab4f-18a4caa5dce8" style="text-align:center">
       <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Untitled 3.png" style="width:480px"/>
       <figcaption>
        <a href="https://hypar.github.io/a00014.html">
         HyPar: 1D Inviscid Burgers Equation - Sine Wave
        </a>
       </figcaption>
      </figure>
     </li>
    </ul>
    <p class="notion-paragraph" id="2bf55b8c-5e5d-4a17-bdc4-9551858318b8">
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal L_{data}
      </span>
     </span>
     <strong>
      : Supervised Learning (SL) Perspective
     </strong>
    </p>
    <p class="notion-paragraph" id="e0ed9dd2-1da2-4d56-831d-371d1efdf0c2">
     Given the dataset
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       D=\{a_i, u_i\}^N_{i=1}
      </span>
     </span>
     as above, it is natural to train the NN
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u_{\theta}
      </span>
     </span>
     to approximate the true solution:
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u_{\theta}(a_i)\approx u_i
      </span>
     </span>
     .
    </p>
    <ul class="bulleted-list" id="f3938c32-4922-411e-8afd-ccc26a7a177f">
     <li class="notion-li" style="list-style-type:disc">
      This objective leads to the SL loss:
      <figure class="equation" id="95f7dea8-224d-4448-a6c8-e4fe39156fb8">
       <div class="equation-container">
        <span class="katex-display">
         <span class="equation_math_block">
          \mathcal L_{data}=\dfrac{1}{N}\sum_{i=1}^N \Vert u_{\theta}(a_i)- u_i \Vert^2
         </span>
        </span>
       </div>
      </figure>
     </li>
    </ul>
    <ul class="bulleted-list" id="1d19b8ff-d6e0-4611-8daa-8ca4b4713661">
     <li class="notion-li" style="list-style-type:disc">
      The SL ensures that the function learned by the NN not only fits the data points but also satisfies the initial and boundary conditions of the problem.
     </li>
    </ul>
    <p class="notion-paragraph" id="330288a7-1205-4936-8186-bb5981ab2985">
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal L_{physic}
      </span>
     </span>
     <strong>
      : INR Perspective
     </strong>
    </p>
    <p class="notion-paragraph" id="a8cb98c3-d021-48ad-8f5a-014ad090b536">
     Inspired by the introduced INR, PINN represents the loss function that encapsulates the physical constraints of the problem, also known as
     <strong>
      physic-informed loss
     </strong>
     .
    </p>
    <p class="notion-paragraph" id="01fb8e39-8a65-450c-b226-883454b862dd">
     This term is crucial as it guides the neural network to learn solutions that not only fit the data but also comply with the underlying physical laws.
    </p>
    <p class="notion-paragraph" id="d0c1870e-6981-460c-bda8-2596f351b83b">
     The required physical constraint can be obtained from PDE (9). We want the residual
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       R
      </span>
     </span>
     to be zero as a constraint:
    </p>
    <figure class="equation" id="3b8d78b0-1c21-4957-9fdb-e9728e0d4990">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        R \coloneqq u_t - \mathcal F (u_{x}, u_{xx}, \cdots,u_{xx...x} ) =0
       </span>
      </span>
     </div>
    </figure>
    <ul class="bulleted-list" id="deabd785-130b-47ab-9da2-ddf5636c70df">
     <li class="notion-li" style="list-style-type:disc">
      Concrete example: For the 1D Burgers equation (10), this leads to:
     </li>
    </ul>
    <figure class="equation" id="f35c8b61-727f-4e21-a4f1-9ea8eabb2e68">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        R = \dfrac{\partial u}{\partial t} + u \dfrac{\partial u}{\partial x} - \nu \dfrac{\partial^2 u}{\partial x^2}
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="2e404569-99ab-430a-834c-8784db651c56">
     The corresponding loss is:
    </p>
    <figure class="equation" id="99cf259e-c206-48d5-a49e-5d399098609d">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal L_{physic}=\dfrac{1}{N}\sum_{i=1}^N \Vert R(u_{\theta}(a_i)) \Vert^2 =\dfrac{1}{N}\sum_{i=1}^N \Vert \dfrac{\partial u_{\theta}(a_i)}{\partial t} - \mathcal{F}(\dfrac{\partial u_{\theta}(a_i)}{\partial x},\cdots)\Vert^2
       </span>
      </span>
     </div>
    </figure>
    <p class="notion-paragraph" id="5cccecb8-b9b9-43f1-86bb-f5bb3bd3b57e">
     Since
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       u_{\theta}
      </span>
     </span>
     is a continuous representation of the dynamics (i.e., INR), this kind of loss is possible.
    </p>
    <p class="notion-paragraph" id="a1d15589-6f14-4de0-9c27-4046768b4437">
     <strong>
      Total Framework:
     </strong>
    </p>
    <figure class="image" id="5e0a9cbe-bd04-451d-bb47-567803f181ea">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Untitled 4.png" style="width:720px"/>
     <figcaption>
      <a href="https://physicsbaseddeeplearning.org/physicalloss.html">
       Physical Loss Terms — Physics-based Deep Learning (physicsbaseddeeplearning.org)
      </a>
     </figcaption>
    </figure>
    <h3 class="" id="89f412ce-91dd-44fe-ab90-4c5f282d2b76" style="font-weight: 650;">
     Related Topics
    </h3>
    <p class="notion-paragraph" id="828f265e-bfcc-4ecf-9b26-122a9a0ceec4">
     <strong>
      Theoretical Considerations:
     </strong>
    </p>
    <ul class="bulleted-list" id="77c8eee3-e52b-401e-904b-a675c857aa56">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Not Generally Applicable to Any PDE:
      </strong>
      There are numerous reported failure modes when applying PINNs to different types of PDEs.
     </li>
    </ul>
    <ul class="bulleted-list" id="95565824-4b55-4031-9989-c5b7225be81f">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       Unstable Training:
      </strong>
      The training process for PINNs can be unstable, making it difficult to achieve convergence and reliable results across various problem settings.
      <ul class="bulleted-list" id="d379c90b-3ec7-419d-9212-e5eeb82ffd9f">
       <li class="notion-li" style="list-style-type:circle">
        <strong>
         <a href="https://openreview.net/forum?id=cy1TKLRAEML">
          Is $L^2$ Physics Informed Loss Always Suitable for Training Physics Informed Neural Network?
         </a>
        </strong>
        <strong>
         (NeurIPS 2022)
        </strong>
        presents the following theoretical results:
        <figure class="image" id="83dd7997-d3f3-4a75-838d-30b7a8794538">
         <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-11_at_5.15.46_PM.png" style="width:589px"/>
        </figure>
        <figure class="image" id="d8d80517-c875-448f-9394-3186951f9f44">
         <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-11_at_5.16.59_PM.png" style="width:589px"/>
        </figure>
        <ul class="bulleted-list" id="34d11734-a760-4f02-982d-847d427f389a">
         <li class="notion-li" style="list-style-type:square">
          When
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            p
           </span>
          </span>
          is small, such as
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            p=2
           </span>
          </span>
          , which is standard for squared loss of residual as above, stable approximation cannot be obtained for some types of PDEs.
         </li>
        </ul>
        <ul class="bulleted-list" id="290a6c7f-7f25-4431-a5c9-01dfd8fe4740">
         <li class="notion-li" style="list-style-type:square">
          This implies that the conventional choice of
          <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
           <span class="equation_math">
            L^2
           </span>
          </span>
          norm in the loss function may lead to instability in certain scenarios, necessitating careful design choices for PINNs.
         </li>
        </ul>
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="35870f89-3648-4b2c-ac56-97a5995c9af4">
     <strong>
      Alternative for Physical Constraint: Differentiable Numerical Simulations
     </strong>
    </p>
    <figure class="image" id="4a8d2299-5151-4859-bda1-f594d8eeaad4" style="text-align:center">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Untitled 5.png" style="width:672px"/>
     <figcaption>
      <a href="https://physicsbaseddeeplearning.org/diffphys.html">
       Introduction to Differentiable Physics — Physics-based Deep Learning (physicsbaseddeeplearning.org)
      </a>
     </figcaption>
    </figure>
    <p class="notion-paragraph" id="1507220f-cec1-4eaa-ae63-a834ca0169ee">
     These simulations offer a promising theoretical perspective, providing a robust framework for incorporating physical constraints directly into the learning process.
    </p>
    <p class="notion-paragraph" id="617d7ce0-a6db-4635-8be5-a3826b22f369">
     Seems better in the theoretical perspective. But computationally infeasible in many cases.
    </p>
    <p class="notion-paragraph" id="6c09247c-87ba-4563-aa42-c427b298c911">
     Relevant Resources:
    </p>
    <ul class="bulleted-list" id="e29dce18-17f7-4557-b864-8ae0ae6a7ceb">
     <li class="notion-li" style="list-style-type:disc">
      TORAX:
      <strong>
      </strong>
      <a href="https://github.com/google-deepmind/torax">
       google-deepmind/torax: TORAX: Tokamak transport simulation in JAX
      </a>
      <ul class="bulleted-list" id="ee152535-a135-4023-b187-bb9953065f39">
       <li class="notion-li" style="list-style-type:circle">
        A differentiable tokamak core transport simulator designed for plasma physics research.
       </li>
      </ul>
     </li>
    </ul>
    <ul class="bulleted-list" id="1430ac89-4e3b-4e8f-bbc2-e04100afc14f">
     <li class="notion-li" style="list-style-type:disc">
      torchdiffeq:
      <a href="https://github.com/rtqichen/torchdiffeq">
       rtqichen/torchdiffeq: Differentiable ODE solvers with full GPU support and O(1)-memory backpropagation. (github.com)
      </a>
      <ul class="bulleted-list" id="c67d7657-8af8-4378-b94c-6132e046b21f">
       <li class="notion-li" style="list-style-type:circle">
        Differentiable ODE solvers which can be combined with Neural ODEs as introduced above.
       </li>
      </ul>
     </li>
    </ul>
    <p class="notion-paragraph" id="f64c1bcf-38d0-483e-9726-bb8bdeb98503">
     <strong>
      Variations of PINNs: A Simple Example
     </strong>
    </p>
    <p class="notion-paragraph" id="3b1e35f9-9792-4875-a14f-e6097a43a9f3">
     There are many variations of PINNs. Here's a simple one:
    </p>
    <ul class="bulleted-list" id="ffc59a28-4bbc-4cf2-a4f5-35490da8132b">
     <li class="notion-li" style="list-style-type:disc">
      <strong>
       <a href="https://openreview.net/forum?id=z9SIj-IM7tn">
        Competitive Physics Informed Networks | OpenReview
       </a>
      </strong>
      <strong>
       (ICLR 2023)
      </strong>
     </li>
    </ul>
    <ul class="bulleted-list" id="6f5cd8c1-7eff-4f28-a374-c63f2eb79c7b">
     <li class="notion-li" style="list-style-type:disc">
      The below figures and formulas are all from the original paper.
     </li>
    </ul>
    <p class="notion-paragraph" id="26a3f598-6043-409f-b3aa-68fcf8f9a132">
     Typical PDE Formulation:
    </p>
    <figure class="image" id="ecaf4b64-531e-444e-bf4b-6fef1f204cb1">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-11_at_6.06.36_PM.png" style="width:720px"/>
    </figure>
    <p class="notion-paragraph" id="422f59be-123d-45e0-bfae-4d68e77f66a0">
     Typical PINN loss with NN
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       \mathcal P
      </span>
     </span>
     :
    </p>
    <figure class="equation" id="28914eac-1958-4fda-9a17-938f4a92e185">
     <div class="equation-container">
      <span class="katex-display">
       <span class="equation_math_block">
        \mathcal L^{PINN}(\mathcal P, x, \bar{x}) = \mathcal L^{PINN}_Ω(\mathcal P, x) + \mathcal L^{PINN}_{∂Ω}(\mathcal P, \bar{x})
       </span>
      </span>
     </div>
    </figure>
    <figure class="image" id="8ec402a2-37df-4527-96da-4361473a97a9">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-11_at_6.07.58_PM.png" style="width:720px"/>
    </figure>
    <ul class="bulleted-list" id="abcebe82-3035-4b22-a1fe-e38fc50b4033">
     <li class="notion-li" style="list-style-type:disc">
      The equation (4) is physic-informed loss and the equation (5) is supervised loss.
     </li>
    </ul>
    <p class="notion-paragraph" id="deac33a7-2fe5-4a15-8d93-d529fb5e2ca4">
     Given the typical PINN framework as above, this paper
     <strong>
      proposes CPINNs which modifies this by introducing discriminator network
     </strong>
     <span class="notion-text-equation-token" contenteditable="false" data-token-index="0" style="user-select:all;-webkit-user-select:all;-moz-user-select:all">
      <span class="equation_math">
       D
      </span>
     </span>
     .
    </p>
    <figure class="image" id="d4956fb9-be8a-4698-aac9-1f5dfb2f13fb">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-11_at_5.53.30_PM.png" style="width:576px"/>
    </figure>
    <p class="notion-paragraph" id="a08ed2fa-6cd6-4bb9-aac3-92022d2864f8">
     <strong>
      Discriminator act as a point weight function
     </strong>
     following with a min-max optimization. It leads to the below objective:
    </p>
    <figure class="image" id="cc6bd565-e190-41da-a54e-dbe03b827545">
     <img src="/pages/posts/002_Neural_Solver_Towards_Future_of_Simulation_Exploration/content/Screenshot_2024-07-11_at_6.13.55_PM.png" style="width:624px"/>
    </figure>
    <ul class="bulleted-list" id="71810ed8-eedb-4d52-b218-6c613a4c7fbd">
     <li class="notion-li" style="list-style-type:disc">
      The equation (7) is a new physic-informed loss and the equation (8) is a new supervised loss.
     </li>
    </ul>
    <p class="notion-paragraph" id="5ef8b1fc-6808-4c21-8564-b02b0950858d">
    </p>
   </div>
  </article>
  <span class="sans" style="font-size:14px;padding-top:2em">
  </span>
 </body>
</html>
