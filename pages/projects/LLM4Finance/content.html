---
layout: page
permalink: pages/projects/LLM4Finance/content.html
---

<!-- Brief Introduce -->
<h2 style="margin-top: 0px">LLM4Finance</h2>
<hr />

<!-- Display date -->
<div style="margin-top: 5px; margin-bottom: 10px">
  <span class="secondary-info"> May 2023 - Current </span>
</div>

<!-- Brief introduction -->
<div class="quote-line" style="margin-top: 20px">
  Developing an LLM-powered finance dashboard to optimize personal investment
  strategies, incorporating financial engineering techniques.
</div>

<!-- Content -->
<!-- Add image of outline. -->
<details style="margin-top: 25px">
  <summary>
    <h3 style="margin: 0; margin-bottom: 20px; font-weight: 550">
      Pipeline Outline
    </h3>
  </summary>
  <img
    src="{{ site.baseurl }}{% link pages/projects/LLM4Finance/overview.png%}"
    class="img-fluid"
    alt="Outline of LLM4Finance"
  />
</details>

<!-- Introduce MLOps pipeline -->
<details>
  <summary>
    <h3 style="margin: 0; margin-bottom: 20px; font-weight: 550">
      MLOps Pipeline for Sentiment Analysis
    </h3>
  </summary>
  <ul style="padding-left: 20px">
    <li>
      PEFT-based LLM predicts news sentiment: positive, neutral, or negative.
    </li>
    <ul style="padding-left: 20px; margin-bottom: 10px">
      <li>
        Continuous training triggers in Hugging Face Spaces once new labeled
        data exceeds a threshold.
      </li>
    </ul>
    <li>Continuous deployment in a separate Hugging Face Space.</li>
    <ul style="padding-left: 20px; margin-bottom: 10px">
      <li>Model quantized via llama.cpp for CPU-based deployment.</li>
    </ul>
    <li>Hugging Face Model Hub used for saving/loading trained models.</li>
  </ul>
</details>

<!-- Introduce LLM application -->
<details>
  <summary>
    <h3 style="margin: 0; margin-bottom: 20px; font-weight: 550">
      LLM Application
    </h3>
  </summary>
  <ul style="padding-left: 20px">
    <li>
      LLM agents automate data processing: sentiment labeling and quality
      control.
    </li>
    <ul style="padding-left: 20px; margin-bottom: 10px">
      <li>
        Addressed performance issues from data quality errors (punctuation,
        duplication).
      </li>
    </ul>
    <li>LLM assistant streamlines financial statement analysis.</li>
    <ul style="padding-left: 20px; margin-bottom: 10px">
      <li>Experimental: RAG-based framework for enhanced analysis.</li>
    </ul>
    <li>
      Experimental: Multi-agent LLM simulates investment environments tailored
      to different investor personas.
    </li>
  </ul>
</details>
