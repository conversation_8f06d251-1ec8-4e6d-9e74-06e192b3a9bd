---
layout: page
---

<h2 style="margin-top: 0px">Growth Hackers: Business Data Analysts</h2>
<hr />

<!-- Display date -->
<div style="margin-top: 5px; margin-bottom: 10px">
  <span class="secondary-info">Mar 2020 - Dec 2020</span>
</div>

<!-- Brief introduction -->
<div class="quote-line" style="margin-top: 20px; margin-bottom: -10px">
  Collaborated with industry partners on real-world data challenges in analysis
  and modeling.

  <p style="margin-top: 10px">
    As 7th education team leader, redesigned the curriculum to enhance internal
    training.
  </p>
</div>

<!-- Project Lists -->

<details>
  <summary>
    <h3 style="margin: 0; font-weight: 550">RecSys Modeling with Educast</h3>
  </summary>

  <div style="margin-top: 5px; margin-bottom: 5px">
    <span class="secondary-info"> Oct 2020 - Dec 2020 </span>
  </div>

  <ul style="padding-left: 25px">
    <li>Developed and compared various RecSys models.</li>
    <ul style="padding-left: 15px">
      <li>Implemented deep generative RecSys in PyTorch.</li>
      <li>Built content-based RecSys using embeddings and clustering.</li>
    </ul>
    <li>Led technical analysis using multiple evaluation metrics.</li>
  </ul>
</details>

<details>
  <summary>
    <h3 style="margin: 0; font-weight: 550">
      Automating Delivery Area Assignment & Cargo Demand Prediction with
      Timfresh
    </h3>
  </summary>

  <div style="margin-top: 5px; margin-bottom: 5px">
    <span class="secondary-info"> Jul 2020 - Aug 2020 </span>
  </div>

  <ul style="padding-left: 25px">
    <li>Led technical aspects as co-PM.</li>
    <li>
      Compared automated clustering-based delivery area assignment to manual
      methods.
    </li>
    <li>
      Evaluated traditional vs. DL-based demand forecasting models and advised
      on data improvements.
    </li>
  </ul>
</details>

<details>
  <summary>
    <h3 style="margin: 0; font-weight: 550">RecSys Modeling with Mathpresso</h3>
  </summary>

  <div style="margin-top: 5px; margin-bottom: 5px">
    <span class="secondary-info"> Apr 2020 - Jun 2020 </span>
  </div>

  <ul style="padding-left: 25px">
    <li>Built a predictive RecSys model in TensorFlow.</li>
    <li>
      Analyzed model embeddings to extract explainable performance insights.
    </li>
    <li>
      Advised on DL-based RecSys effectiveness for different data scenarios.
    </li>
  </ul>
</details>
