---
layout: post
---

<section class="content-box">
  <h2 style="margin: 0">Personal Projects</h2>
  <hr / style="margin-bottom: 0px">
</section>

<!-- Project Lists -->

<section class="content-box" style="position: relative; padding-bottom: 20px">
  <h3 class="post-title">
    <a
      href="{{ site.baseurl }}{% link pages/projects/LLM4Finance/content.html %}"
      >LLM4Finance</a
    >
  </h3>
  <h6 class="post-date">Date: Since May 2023</h6>

  <!-- Display Tags and Button in One Row -->
  <div class="tags-and-button">
    {% assign key = post_info[1].path %} {% assign tags =
    site.data.tags["pages/projects/LLM4Finance/content.html"] %} {% if tags %}
    <p class="post-tags">
      <i class="fa fa-tags tag-icon"></i>Tags: 
      {% for tag in tags %} 
        <a href="{{ site.baseurl }}/tags/{{ tag | slugify }}/">{{ tag }}</a>{% if forloop.last == false %}, {% endif %} 
      {% endfor %}
    </p>
    {% else %}
    <p class="post-tags"><i class="fa fa-tags tag-icon"></i>Tags: None</p>
    {% endif %}

    <a
      href="{{ site.baseurl }}{% link pages/projects/LLM4Finance/content.html %}"
      class="read-more-btn"
    >
      <i class="fa fa-book"></i> Read More
    </a>
  </div>
</section>

<!-- <details>
  <summary>
    <h3 style="margin: 0; font-weight: 650">Video2Text Summarization</h3>
  </summary>

  <div style="margin-top: 5px; margin-bottom: 5px">
    <span class="secondary-info"> October 2024 - Current </span>
  </div>

  <ul style="padding-left: 25px">
    <li>Motivated by the video summarization product of "Lilys AI".</li>
    <li>
      Aim to build a video-to-text summarization service using customized
      multimodal deep learning models tailored to my summary style.
    </li>
  </ul>
</details> -->
