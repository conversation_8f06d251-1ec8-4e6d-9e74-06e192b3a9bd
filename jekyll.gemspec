Gem::Specification.new do |spec|
    spec.name          = "sku-custom-jekyll-theme"
    spec.version       = "0.0.0"
    spec.authors       = ["Ki-Ung Song"]
    spec.email         = ["<EMAIL>"]
  
    spec.summary       = "A custom Jekyll theme for studying purposes"
    spec.license       = "MIT"

    spec.files         = `git ls-files -z`.split("\x0").select do |f| 
        f.match(%r{^(assets|_layouts|_includes|LICENSE|README|feed|_data|tags|staticman)}i)
    end

    spec.add_runtime_dependency "jekyll", ">= 3.7", "< 5.0"
    spec.add_runtime_dependency "jekyll-paginate", "~> 1.1"
    spec.add_runtime_dependency "jekyll-sitemap", "~> 1.4"

    ### From minimal mistakes
    spec.add_runtime_dependency "jekyll-gist", "~> 1.5"
    spec.add_runtime_dependency "jekyll-feed", "~> 0.1"
    spec.add_runtime_dependency "jekyll-include-cache", "~> 0.1"

    ### From beautiful-jekyll
    # "kramdown-parser-gfm" and "kramdown", are related to processing
    # and converting Markdown content in Ruby applications, 
    # particularly useful in static site generators like Jekyll.
    spec.add_runtime_dependency "kramdown-parser-gfm", "~> 1.1"
    spec.add_runtime_dependency "kramdown", "~> 2.3.2"

    spec.add_runtime_dependency "webrick", "~> 1.8"

    spec.add_development_dependency "bundler"
    spec.add_development_dependency "rake", ">= 12.3.3"
end