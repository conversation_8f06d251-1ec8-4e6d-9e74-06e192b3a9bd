############################
# --- Required options --- #
############################

# Name of website
title: <PERSON><PERSON>Un<PERSON> Song

# Your name to show in the footer
author: <PERSON><PERSON><PERSON><PERSON> Song

# Contents for introduction card
subtitle: Problem Solver with AI and Mathematics Who Wants to Change the World
location: Seoul, South Korea

# Website URL
url: "https://kiungsong.github.io/"

###############################################
# --- List of links in the navigation bar --- #
###############################################

navbar-links:
  About Me: "index"
  CV: "assets/CV_KiUngSong.pdf" # Downloadable CV
  Experience: "pages/experience"
  Publications: "pages/publication"
  Posts: "pages/post"

################
# --- Logo --- #
################

# Image to show in the navigation bar - works best with a square image
# Remove this parameter if you don't want an image in the navbar
avatar: "/assets/img/profile/profile.jpg"

#####################################
# --- Footer social media links --- #
#####################################

# Select the social network links that you want to show in the footer.
# You can change the order that they show up on the page by changing the order here.
# Uncomment the links you want to show and add your information to each one.
social-network-links:
  email: "<EMAIL>"
  github: KiUngSong
  google-scholar: O3LAPDUAAAAJ
  linkedin: ki-ung-song
  # facebook: kiung.song.9
  # rss: true  # remove this line if you don't want to show an RSS link at the bottom
  # youtube: your channel
  # telephone: +14159998888
  # ORCID: your ORCID ID

# If you want your website to generate an RSS feed, provide a description
# The URL for the feed will be https://<your_website>/feed.xml
# rss-description: This website is a virtual proof that I'm awesome

###########################
# --- General options --- #
###########################

# To enable custom Jekyll plugins.
safe: false

# How to display the link to your website in the footer
# Remove this if you don't want a link in the footer
# url-pretty: "MyWebsite.com"

# Add the website title to the title of every page
title-on-all-pages: true

# Excerpt word length - Truncate the excerpt of each post on the feed page to the specified number of words
excerpt_length: 50

# The keywords to associate with your website, for SEO purposes
#keywords: "my,list,of,keywords"

#################################
# --- Web Analytics Section --- #
#################################

# Fill in your Google Analytics tag ID (or "Measurement ID") to track your website usage
#gtag: "G-XXXXXXXXXX"

# Google Tag Manager ID
#gtm: ""

# Google Universal Analytics ID -- deprecated
# As of July 2023 this is no longer supported by Google! If you are still using `google_analytics`,
# you should switch to using the `gtag` field above instead.
#google_analytics: "UA-XXXXXXXX-X"

####################
# --- Comments --- #
####################

# To use Disqus comments, sign up to https://disqus.com and fill in your Disqus shortname (NOT the userid)
#disqus: ""

# To use Facebook Comments, create a Facebook app and fill in the Facebook App ID
#fb_comment_id: ""

# To use CommentBox, sign up for a Project ID on https://commentbox.io
#commentbox: "" # Project ID, e.g. "5694267682979840-proj"

# To use Utterances comments: (0) uncomment the following section, (1) fill in
# "repository" (make sure the repository is public), (2) Enable Issues in your repository,
# (3) Install the Utterances app in your repository https://github.com/apps/utterances
# See more details about the parameters below at https://utteranc.es/
#utterances:
#  repository: # GitHub username/repository eg. "daattali/beautiful-jekyll"
#  issue-term: title   # Mapping between blog posts and GitHub issues
#  theme: github-light # Utterances theme
#  label: blog-comments # Label that will be assigned to GitHub Issues created by Utterances

# To use Staticman comments, uncomment the following section. You may leave the reCaptcha
# section commented if you aren't using reCaptcha for spam protection.
# Using Staticman requires advanced knowledge, please consult
# https://github.com/eduardoboucas/staticman/ and https://staticman.net/ for further
# instructions. For any support with staticman please direct questions to staticman and
# not to BeautifulJekyll.
#staticman:
#  repository : # GitHub username/repository eg. "daattali/beautiful-jekyll"
#  branch     : master # If you're not using `master` branch, then you also need to update the `branch` parameter in `staticman.yml`
#  endpoint   : # URL of your deployment, with a trailing slash eg. "https://<your-api>/v3/entry/github/"
#  reCaptcha:   # (optional, set these parameters in `staticman.yml` as well)
#    siteKey  : # You need to apply for a site key on Google
#    secret   : # Encrypt your password by going to https://<your-own-api>/v3/encrypt/<your-site-secret>

# To use giscus comments:
# (0) Uncomment the following giscus section, (1) Enable Discussions in your GitHub repository,
# (2) Install the giscus app in your repository (details at https://giscus.app),
# (3) Fill in *all* the parameters below
# See more details about giscus and each of the following parameters at https://giscus.app
#giscus:
#  hostname: giscus.app # Replace with your giscus instance's hostname if self-hosting
#  repository: # GitHub username/repository eg. "daattali/beautiful-jekyll"
#  repository-id: # ID of your repository, retrieve this info from https://giscus.app
#  category: Announcements # Category name of your GitHub Discussion posts
#  category-id: # ID of your category, retrieve this info from https://giscus.app
#  mapping: pathname
#  reactions-enabled: 1
#  emit-metadata: 0
#  theme: light

################
# --- Misc --- #
################

# Ruby Date Format to show dates of posts
date_format: "%B %-d, %Y"

#################################################################################
# --- You don't need to touch anything below here (but you can if you want) --- #
#################################################################################

# Output options (more information on Jekyll's site)
timezone: "America/Toronto"
markdown: kramdown
highlighter: rouge
paginate: 5

kramdown:
  input: GFM

# Exclude these files from production site
exclude:
  - CNAME
  - Gemfile
  - Gemfile.lock
  - docs/

# Plugins
plugins:
  - jekyll-paginate
  - jekyll-feed
  - jekyll-sitemap
  - jekyll-seo-tag
